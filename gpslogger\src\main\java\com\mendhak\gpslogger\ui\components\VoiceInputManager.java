/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import androidx.core.content.ContextCompat;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.PreferenceHelper;
import org.slf4j.Logger;

import java.util.Locale;

/**
 * Manages voice input functionality for annotation buttons
 */
public class VoiceInputManager {
    
    private static final Logger LOG = Logs.of(VoiceInputManager.class);
    public static final int VOICE_INPUT_REQUEST_CODE = 1001;
    
    private final Activity activity;
    private final androidx.fragment.app.Fragment fragment;
    private final PreferenceHelper preferenceHelper;
    private VoiceInputListener listener;
    private boolean isRecording = false;
    private AudioRecordingManager audioRecordingManager;
    private UnifiedVoiceInputManager unifiedVoiceInputManager;
    private String pendingAnnotationText; // Store annotation text for audio filename

    // 配置选项：是否使用统一音频处理器
    private static final boolean USE_UNIFIED_PROCESSOR = true;
    
    /**
     * Interface for voice input callbacks
     */
    public interface VoiceInputListener {
        void onVoiceInputStarted();
        void onVoiceInputResult(String text);
        void onVoiceInputError(String error);
        void onVoiceInputCancelled();
    }
    
    public VoiceInputManager(androidx.fragment.app.Fragment fragment, VoiceInputListener listener) {
        this.fragment = fragment;
        this.activity = fragment.getActivity();
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeAudioRecording();
    }

    /**
     * Constructor for Activity-based usage
     */
    public VoiceInputManager(Activity activity, VoiceInputListener listener) {
        this.activity = activity;
        this.fragment = null;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeAudioRecording();
    }

    /**
     * Initialize audio recording manager
     */
    private void initializeAudioRecording() {
        Context context = activity != null ? activity : (fragment != null ? fragment.getContext() : null);
        System.out.println("=== VoiceInputManager: initializeAudioRecording called ===");
        LOG.info("=== VoiceInputManager: initializeAudioRecording called ===");

        if (context != null) {
            if (USE_UNIFIED_PROCESSOR) {
                // 使用新的统一音频处理器
                System.out.println("=== VoiceInputManager: Creating UnifiedVoiceInputManager ===");
                LOG.info("Using unified audio processor to avoid conflicts");

                UnifiedVoiceInputManager.VoiceInputListener unifiedListener = new UnifiedVoiceInputManager.VoiceInputListener() {
                    @Override
                    public void onVoiceInputStarted() {
                        LOG.debug("Unified voice input started");
                        if (listener != null) {
                            listener.onVoiceInputStarted();
                        }
                    }

                    @Override
                    public void onVoiceInputResult(String text) {
                        LOG.info("Unified voice input result: '{}'", text);
                        if (listener != null) {
                            listener.onVoiceInputResult(text);
                        }
                    }

                    @Override
                    public void onVoiceInputError(String error) {
                        LOG.error("Unified voice input error: {}", error);
                        if (listener != null) {
                            listener.onVoiceInputError(error);
                        }
                    }

                    @Override
                    public void onVoiceInputCancelled() {
                        LOG.debug("Unified voice input cancelled");
                        if (listener != null) {
                            listener.onVoiceInputCancelled();
                        }
                    }

                    @Override
                    public void onRecordingStarted(String filePath) {
                        LOG.debug("Unified recording started: {}", filePath);
                    }

                    @Override
                    public void onRecordingStopped(String filePath) {
                        LOG.info("Unified recording saved: {}", filePath);
                    }
                };

                if (fragment != null) {
                    unifiedVoiceInputManager = new UnifiedVoiceInputManager(fragment, unifiedListener);
                } else {
                    unifiedVoiceInputManager = new UnifiedVoiceInputManager(activity, unifiedListener);
                }

            } else {
                // 使用传统的分离式录音方案
                System.out.println("=== VoiceInputManager: Creating AudioRecordingManager ===");
                LOG.info("Using traditional separate audio recording");

                audioRecordingManager = new AudioRecordingManager(context, new AudioRecordingManager.AudioRecordingListener() {
                    @Override
                    public void onRecordingStarted(String filePath) {
                        LOG.debug("Audio recording started: {}", filePath);
                    }

                    @Override
                    public void onRecordingStopped(String filePath) {
                        LOG.info("Audio recording saved: {}", filePath);
                    }

                    @Override
                    public void onRecordingError(String error) {
                        LOG.error("Audio recording error: {}", error);
                    }

                    @Override
                    public void onRecordingStatusUpdate(String status) {
                        LOG.info("Audio recording status: {}", status);
                    }
                });
            }
        } else {
            LOG.warn("Cannot initialize audio recording - no context available");
        }
    }
    
    /**
     * Check if voice input is available on this device
     */
    public boolean isVoiceInputAvailable() {
        try {
            if (activity == null) {
                LOG.warn("Activity is null, voice input not available");
                return false;
            }
            PackageManager pm = activity.getPackageManager();
            if (pm == null) {
                LOG.warn("PackageManager is null, voice input not available");
                return false;
            }

            // Method 1: Check using standard RecognizerIntent
            Intent intent1 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            java.util.List<android.content.pm.ResolveInfo> activities1 = pm.queryIntentActivities(intent1, 0);
            LOG.debug("Standard RecognizerIntent check: found " + activities1.size() + " activities");

            // Method 2: Check with more specific intent
            Intent intent2 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent2.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            java.util.List<android.content.pm.ResolveInfo> activities2 = pm.queryIntentActivities(intent2, 0);
            LOG.debug("Specific RecognizerIntent check: found " + activities2.size() + " activities");

            // Method 3: Check using PackageManager.MATCH_DEFAULT_ONLY flag
            Intent intent3 = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            java.util.List<android.content.pm.ResolveInfo> activities3 = pm.queryIntentActivities(intent3, PackageManager.MATCH_DEFAULT_ONLY);
            LOG.debug("Default-only RecognizerIntent check: found " + activities3.size() + " activities");

            // Method 4: Check for SpeechRecognizer service availability
            boolean speechRecognizerAvailable = android.speech.SpeechRecognizer.isRecognitionAvailable(activity);
            LOG.debug("SpeechRecognizer.isRecognitionAvailable: " + speechRecognizerAvailable);

            // Method 5: Check for specific known speech recognition apps
            boolean knownAppsAvailable = checkKnownSpeechApps(pm);
            LOG.debug("Known speech apps available: " + knownAppsAvailable);

            // Combine all methods
            boolean available = activities1.size() > 0 || activities2.size() > 0 || activities3.size() > 0 || speechRecognizerAvailable || knownAppsAvailable;

            LOG.debug("Final voice input availability: " + available);

            // Log all available speech recognition apps for debugging
            java.util.Set<String> allApps = new java.util.HashSet<>();
            for (android.content.pm.ResolveInfo info : activities1) {
                allApps.add(info.activityInfo.packageName);
            }
            for (android.content.pm.ResolveInfo info : activities2) {
                allApps.add(info.activityInfo.packageName);
            }
            for (android.content.pm.ResolveInfo info : activities3) {
                allApps.add(info.activityInfo.packageName);
            }

            for (String packageName : allApps) {
                LOG.debug("Available speech recognition app: " + packageName);
            }

            return available;
        } catch (Exception e) {
            LOG.error("Error checking voice input availability", e);
            return false;
        }
    }

    /**
     * Check for known speech recognition apps
     */
    private boolean checkKnownSpeechApps(PackageManager pm) {
        String[] knownSpeechApps = {
            "com.google.android.googlequicksearchbox", // Google App
            "com.google.android.voicesearch", // Google Voice Search
            "com.iflytek.speechcloud", // 讯飞语记
            "com.iflytek.inputmethod", // 讯飞输入法
            "com.baidu.input", // 百度输入法
            "com.sohu.inputmethod.sogou", // 搜狗输入法
            "com.samsung.android.bixby.agent", // Samsung Bixby
            "com.microsoft.cortana", // Microsoft Cortana
            "com.amazon.dee.app" // Amazon Alexa
        };

        for (String packageName : knownSpeechApps) {
            try {
                pm.getPackageInfo(packageName, 0);
                LOG.debug("Found known speech app: " + packageName);
                return true;
            } catch (PackageManager.NameNotFoundException e) {
                // App not installed, continue checking
            }
        }

        return false;
    }
    
    /**
     * Check if RECORD_AUDIO permission is granted
     */
    public boolean hasRecordAudioPermission() {
        return ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * Start voice input if conditions are met
     */
    public boolean startVoiceInput() {
        System.out.println("=== VoiceInputManager: startVoiceInput called ===");
        LOG.debug("Starting voice input");

        if (activity == null) {
            LOG.error("Activity is null, cannot start voice input");
            return false;
        }

        if (preferenceHelper == null) {
            LOG.error("PreferenceHelper is null, cannot start voice input");
            return false;
        }

        // Check if voice input is enabled in settings
        if (!preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is disabled in settings");
            return false;
        }

        // 使用统一音频处理器
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            LOG.info("Starting voice input with unified audio processor");
            return unifiedVoiceInputManager.startVoiceInput();
        }
        
        // Check if voice input is available
        boolean voiceAvailable = isVoiceInputAvailable();
        LOG.debug("Voice input availability: " + voiceAvailable);

        if (!voiceAvailable) {
            LOG.warn("Voice input detection failed, but will still attempt to start");
            // Don't return false immediately, try to start anyway
            // Some devices/apps might not be detected properly but still work
        }
        
        // Check permissions
        if (!hasRecordAudioPermission()) {
            LOG.warn("RECORD_AUDIO permission not granted");
            if (listener != null) {
                listener.onVoiceInputError("需要录音权限才能使用语音输入功能");
            }
            return false;
        }
        
        try {
            Intent intent = createVoiceInputIntent();

            // Try to start the voice input activity using Fragment
            LOG.debug("Attempting to start voice input activity via Fragment");
            if (fragment != null) {
                fragment.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Fragment.startActivityForResult");
            } else {
                activity.startActivityForResult(intent, VOICE_INPUT_REQUEST_CODE);
                LOG.debug("Started via Activity.startActivityForResult (fallback)");
            }
            isRecording = true;

            // Start audio recording with a slight delay to avoid conflicts with voice recognition initialization
            if (audioRecordingManager != null) {
                // Double-check RECORD_AUDIO permission for audio recording
                if (audioRecordingManager.hasRecordAudioPermission()) {
                    // Use a small delay to let voice recognition initialize first
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (isRecording) { // Only start if voice recognition is still active
                                boolean recordingStarted = audioRecordingManager.startRecording();
                                if (recordingStarted) {
                                    LOG.info("Audio recording started with delay after voice recognition");
                                } else {
                                    LOG.warn("Failed to start delayed audio recording");
                                }
                            } else {
                                LOG.debug("Voice recognition already stopped, skipping audio recording");
                            }
                        }
                    }, 200); // 200ms delay
                } else {
                    LOG.warn("RECORD_AUDIO permission not granted for audio recording, voice recognition only");
                }
            } else {
                LOG.warn("AudioRecordingManager not available, voice recognition only");
            }

            if (listener != null) {
                listener.onVoiceInputStarted();
            }

            LOG.debug("Voice input started successfully");
            return true;

        } catch (android.content.ActivityNotFoundException e) {
            LOG.error("No activity found to handle voice input intent", e);
            if (listener != null) {
                listener.onVoiceInputError("未找到语音识别应用。请安装讯飞语记、Google语音输入或其他语音识别应用。");
            }
            return false;
        } catch (Exception e) {
            LOG.error("Error starting voice input", e);
            if (listener != null) {
                listener.onVoiceInputError("启动语音识别失败: " + e.getMessage() + "。请检查语音识别应用是否正常工作。");
            }
            return false;
        }
    }
    
    /**
     * Create the voice input intent
     */
    private Intent createVoiceInputIntent() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

        // Set language preference
        String language = preferenceHelper.getVoiceInputLanguage();
        if (language.isEmpty()) {
            language = Locale.getDefault().toString();
        }
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, language);

        // Set prompts and other options
        intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出注释内容");
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5); // Increase max results for better accuracy
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, activity.getPackageName());

        // Set timeout if specified
        int timeout = preferenceHelper.getVoiceInputTimeout();
        if (timeout > 0) {
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
        }

        // Add additional compatibility flags
        intent.addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);

        LOG.debug("Created voice input intent with language: " + language + ", timeout: " + timeout + "s");

        return intent;
    }
    
    /**
     * Handle the result from voice input activity
     */
    public void handleActivityResult(int requestCode, int resultCode, Intent data) {
        LOG.debug("=== VOICE INPUT ACTIVITY RESULT ===");
        LOG.debug("Request code: " + requestCode + " (expected: " + VOICE_INPUT_REQUEST_CODE + " or Xunfei: " + VoiceRecognitionAdapter.getXunfeiRequestCode() + ")");
        LOG.debug("Result code: " + resultCode);
        LOG.debug("Data: " + (data != null ? "not null" : "null"));

        // 首先检查是否是统一处理器的结果（包括直接调用讯飞语记）
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            unifiedVoiceInputManager.handleActivityResult(requestCode, resultCode, data);
            return;
        }

        // 处理传统的语音输入结果
        if (requestCode != VOICE_INPUT_REQUEST_CODE) {
            LOG.debug("Request code mismatch, ignoring");
            return;
        }

        isRecording = false;
        LOG.debug("Set isRecording to false");

        if (resultCode == Activity.RESULT_OK && data != null) {
            try {
                java.util.ArrayList<String> results = data.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
                LOG.debug("Extracted results: " + (results != null ? results.size() + " items" : "null"));

                if (results != null && !results.isEmpty()) {
                    String recognizedText = results.get(0);
                    LOG.info("=== VOICE RECOGNITION SUCCESS ===");
                    LOG.info("Recognized text: '" + recognizedText + "'");
                    LOG.info("All results: " + results.toString());

                    // Store the recognized text for later use in audio filename
                    pendingAnnotationText = recognizedText;

                    // Stop audio recording with temporary filename (will be renamed later)
                    if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                        String audioPath = audioRecordingManager.stopRecording(recognizedText);
                        if (audioPath != null) {
                            LOG.info("Audio recording stopped and saved temporarily: {}", audioPath);
                        } else {
                            LOG.warn("Failed to save audio recording");
                        }
                    }

                    if (listener != null) {
                        LOG.debug("Calling listener.onVoiceInputResult()");
                        listener.onVoiceInputResult(recognizedText);
                        LOG.debug("Listener callback completed");
                    } else {
                        LOG.warn("Listener is null, cannot deliver result");
                    }
                } else {
                    LOG.warn("Voice input returned empty results");
                    // Cancel audio recording if no results
                    if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                        audioRecordingManager.cancelRecording();
                        LOG.debug("Cancelled audio recording due to empty voice results");
                    }
                    if (listener != null) {
                        listener.onVoiceInputError("语音识别没有结果，请重试");
                    }
                }
            } catch (Exception e) {
                LOG.error("Error processing voice input result", e);
                // Cancel audio recording on error
                if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                    audioRecordingManager.cancelRecording();
                    LOG.debug("Cancelled audio recording due to processing error");
                }
                if (listener != null) {
                    listener.onVoiceInputError("处理语音识别结果时出错: " + e.getMessage());
                }
            }
        } else if (resultCode == Activity.RESULT_CANCELED) {
            LOG.debug("Voice input was cancelled by user");
            // Cancel audio recording when voice input is cancelled
            if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                audioRecordingManager.cancelRecording();
                LOG.debug("Cancelled audio recording due to voice input cancellation");
            }
            if (listener != null) {
                listener.onVoiceInputCancelled();
            }
        } else {
            LOG.warn("Voice input failed with result code: " + resultCode);
            // Cancel audio recording on failure
            if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                audioRecordingManager.cancelRecording();
                LOG.debug("Cancelled audio recording due to voice input failure");
            }
            if (listener != null) {
                listener.onVoiceInputError("语音识别失败，请重试");
            }
        }
        LOG.debug("=== VOICE INPUT RESULT PROCESSING COMPLETE ===");
    }
    
    /**
     * Stop voice input if currently recording
     */
    public void stopVoiceInput() {
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            LOG.debug("Stopping unified voice input");
            unifiedVoiceInputManager.stopVoiceInput();
        } else {
            // 传统方式的停止逻辑
            if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                audioRecordingManager.stopRecording(pendingAnnotationText);
            }
        }
        isRecording = false;
    }
    
    /**
     * Check if currently recording
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * Set the voice input listener
     */
    public void setListener(VoiceInputListener listener) {
        this.listener = listener;
    }

    /**
     * Rename the last recorded audio file with the final processed annotation text
     * This should be called after template processing is complete
     * @param finalAnnotationText The final processed annotation text from template engine
     */
    public void renameAudioFileWithFinalText(String finalAnnotationText) {
        if (audioRecordingManager == null) {
            LOG.debug("AudioRecordingManager not available, cannot rename audio file");
            return;
        }

        String currentPath = audioRecordingManager.getCurrentRecordingPath();
        if (currentPath == null || currentPath.isEmpty()) {
            LOG.debug("No current recording path, cannot rename audio file");
            return;
        }

        LOG.info("Attempting to rename audio file with final annotation text: '{}'", finalAnnotationText);

        try {
            // Generate new filename based on final annotation text
            String gpsLoggerFolder = preferenceHelper.getGpsLoggerFolder();
            String newFileName = generateFinalAudioFileName(finalAnnotationText);
            java.io.File newFile = new java.io.File(gpsLoggerFolder, newFileName);
            java.io.File currentFile = new java.io.File(currentPath);

            if (currentFile.exists() && !newFile.exists()) {
                boolean renamed = currentFile.renameTo(newFile);
                if (renamed) {
                    LOG.info("Successfully renamed audio file to: {}", newFile.getAbsolutePath());
                } else {
                    LOG.warn("Failed to rename audio file from {} to {}", currentPath, newFile.getAbsolutePath());
                }
            } else {
                LOG.warn("Cannot rename audio file - current file exists: {}, new file exists: {}",
                        currentFile.exists(), newFile.exists());
            }
        } catch (Exception e) {
            LOG.error("Error renaming audio file", e);
        }
    }

    /**
     * Generate final audio filename based on annotation text
     */
    private String generateFinalAudioFileName(String annotationText) {
        if (annotationText == null || annotationText.trim().isEmpty()) {
            // Fallback to timestamp if no annotation text
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault());
            String timestamp = sdf.format(new java.util.Date());
            return "voice_annotation_" + timestamp + ".wav";
        }

        // Clean the annotation text for use as filename
        String cleanText = sanitizeFileName(annotationText.trim());

        // Limit filename length
        if (cleanText.length() > 200) {
            cleanText = cleanText.substring(0, 200);
        }

        // Ensure filename is not empty after cleaning
        if (cleanText.isEmpty()) {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault());
            String timestamp = sdf.format(new java.util.Date());
            return "voice_annotation_" + timestamp + ".3gp";
        }

        return cleanText + ".3gp";
    }

    /**
     * Sanitize text for use as filename
     */
    private String sanitizeFileName(String text) {
        if (text == null) {
            return "";
        }

        // Replace invalid characters with underscore
        String sanitized = text.replaceAll("[\\\\/:*?\"<>|]", "_");

        // Replace multiple consecutive underscores with single underscore
        sanitized = sanitized.replaceAll("_{2,}", "_");

        // Remove leading and trailing underscores
        sanitized = sanitized.replaceAll("^_+|_+$", "");

        return sanitized;
    }

    /**
     * Get the pending annotation text (the original voice recognition result)
     */
    public String getPendingAnnotationText() {
        return pendingAnnotationText;
    }

    /**
     * Clear the pending annotation text
     */
    public void clearPendingAnnotationText() {
        pendingAnnotationText = null;
    }


    /**
     * Cancel voice input
     */
    public void cancelVoiceInput() {
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            LOG.debug("Cancelling unified voice input");
            unifiedVoiceInputManager.cancelVoiceInput();
        } else {
            // 传统方式的取消逻辑
            if (audioRecordingManager != null && audioRecordingManager.isRecording()) {
                audioRecordingManager.cancelRecording();
            }
        }
        isRecording = false;
        pendingAnnotationText = null;
    }

    /**
     * Check if voice input is currently processing
     */
    public boolean isProcessing() {
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            return unifiedVoiceInputManager.isProcessing();
        } else {
            return isRecording;
        }
    }



    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (USE_UNIFIED_PROCESSOR && unifiedVoiceInputManager != null) {
            unifiedVoiceInputManager.cleanup();
            unifiedVoiceInputManager = null;
        }

        if (audioRecordingManager != null) {
            if (audioRecordingManager.isRecording()) {
                audioRecordingManager.cancelRecording();
            }
            audioRecordingManager = null;
        }

        isRecording = false;
        pendingAnnotationText = null;
        LOG.debug("VoiceInputManager cleaned up");
    }
}
