# config file for oc-install-3party-apps
# one line with separator "!" for ...
# NAME = Short text
# CMDLINE = A small line to install and to do all shell things, separate this
#           with a ";". No multiline supported currently

# 1NAME!2CMDLINE

# example: remove the first comment char "#"

# Install official apps.
# Official contacts app!occ app:enable contacts
# Official calendar app!occ app:enable calendar

## tools for installation
#tool installation!apt-get install -y git-core curl
#
### apps
## sms sync
#ownCloud SMS!git clone https://github.com/nerzhul/ocsms.git --branch=stable
#
## youtubedl - with some manipulation things
#Youtube Downloader depencies!apt-get install -y libav-tools python
#Youtube Downloader Binary!curl https://yt-dl.org/downloads/2015.01.25/youtube-dl -o /usr/bin/youtube-dl ; chmod a+x /usr/bin/youtube-dl
#Youtube Downloader App!git clone https://github.com/shibby/owncloud-youtubedl.git ; mv owncloud-youtubedl/youtubedl .; rm -rf owncloud-youtubedl
#
## this tools are not used anymore
#tool remove;apt-get remove -y git-core curl
