## Why has GPSLogger been removed from the Play Store?  
## What happened to the Google Drive feature?  
## What happened to the Activity Recognition feature?  

The app has been [removed from Google Play](https://github.com/mendhak/gpslogger/issues/849) and has [moved to F-Droid](https://f-droid.org/en/packages/com.mendhak.gpslogger/).  As part of [F-Droid's Inclusion Policy](https://f-droid.org/en/docs/Inclusion_Policy/), proprietary libraries are not allowed, only open source libraries are allowed.  This meant that the Activity Recognition feature and the Google Drive feature had to be removed from the app.    

In May 2022 the Google Drive feature has been reimplemented in the app, but the Activity Recognition is unlikely to ever happen, unless an open source library for it gets created.  

There may still be references to Activity Recognition in screenshots, FAQ and dialogs.  Please ignore those for now. 