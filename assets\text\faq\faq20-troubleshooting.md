## Troubleshooting

Sometimes the app might not behave in an expected way.  

You can troubleshoot it yourself by going to *General Options* and enabling the *Write to debug file* option.  

Next, reproduce the behavior or problem, and this creates a `debuglog.txt` file in the GPSLogger folder.  

You can then grab the file off your phone and have a look through it, or email it to yourself from the same screen (*Attach debug log to email*).  Be sure to turn it off afterwards as this file grows very fast.   