
name: Build and Publish Github Pages

# Trigger this workflow only when the main branches, assets folder is modified. 
on:
  push:
    branches: [ "main", "master" ]
    paths: 
      - 'assets/**'
      - 'LICENSE.md'    

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write
  deployments: write

jobs:
  build:
    runs-on: ubuntu-latest

    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    defaults:
      run:
        working-directory: assets/generate-pages

    steps:
      - uses: actions/checkout@v3


      - name: Build the static site
        run: docker compose run --rm --entrypoint "/bin/bash -c 'npm install;npx eleventy'" eleventy

          
      - name: Setup Github Pages
        uses: actions/configure-pages@v2

      - name: Upload Github Pages artifact
        uses: actions/upload-pages-artifact@v1
        with:
          path: 'assets/generate-pages/_site'

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v1        
