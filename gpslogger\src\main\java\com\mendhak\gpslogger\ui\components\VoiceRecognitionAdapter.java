/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音识别适配器 - 处理实时音频数据的语音识别
 * 支持流式音频输入，避免与录音功能的资源冲突
 */
public class VoiceRecognitionAdapter {
    private static final Logger LOG = Logs.of(VoiceRecognitionAdapter.class);
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private Handler mainHandler;
    
    // 语音识别相关
    private SpeechRecognizer speechRecognizer;
    private AtomicBoolean isRecognizing = new AtomicBoolean(false);
    private android.speech.RecognitionListener androidRecognitionListener;
    
    // 音频缓冲相关
    private java.io.ByteArrayOutputStream audioBuffer;
    private long lastAudioTime = 0;
    private static final long AUDIO_TIMEOUT_MS = 3000; // 3秒无音频则超时
    private static final long RECOGNITION_TIMEOUT_MS = 10000; // 10秒识别超时
    
    // 监听器
    private VoiceRecognitionListener listener;
    
    /**
     * 语音识别监听器接口
     */
    public interface VoiceRecognitionListener {
        void onRecognitionResult(String text);
        void onRecognitionError(String error);
        void onRecognitionTimeout();
    }
    
    /**
     * 构造函数
     */
    public VoiceRecognitionAdapter(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.audioBuffer = new java.io.ByteArrayOutputStream();
        
        initializeSpeechRecognizer();
        LOG.debug("VoiceRecognitionAdapter initialized");
    }
    
    /**
     * 设置识别监听器
     */
    public void setRecognitionListener(VoiceRecognitionListener listener) {
        this.listener = listener;
    }
    
    /**
     * 初始化语音识别器
     */
    private void initializeSpeechRecognizer() {
        // 在中国手机上，SpeechRecognizer.isRecognitionAvailable经常返回false
        // 但实际上语音识别功能可能是可用的，所以我们跳过这个检查
        boolean recognitionAvailable = SpeechRecognizer.isRecognitionAvailable(context);
        LOG.debug("SpeechRecognizer.isRecognitionAvailable: {}", recognitionAvailable);

        if (!recognitionAvailable) {
            LOG.warn("SpeechRecognizer.isRecognitionAvailable returned false, but will try to initialize anyway (common on Chinese devices)");
        }

        try {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
            if (speechRecognizer == null) {
                LOG.error("Failed to create SpeechRecognizer - returned null");
                return;
            }
        } catch (Exception e) {
            LOG.error("Exception creating SpeechRecognizer", e);
            return;
        }

        androidRecognitionListener = new android.speech.RecognitionListener() {
            @Override
            public void onReadyForSpeech(Bundle params) {
                LOG.debug("Speech recognizer ready for speech");
            }
            
            @Override
            public void onBeginningOfSpeech() {
                LOG.debug("Beginning of speech detected");
            }
            
            @Override
            public void onRmsChanged(float rmsdB) {
                // 音量变化，可用于UI反馈
            }
            
            @Override
            public void onBufferReceived(byte[] buffer) {
                // 接收到音频缓冲区数据
                LOG.debug("Received audio buffer: {} bytes", buffer.length);
            }
            
            @Override
            public void onEndOfSpeech() {
                LOG.debug("End of speech detected");
            }
            
            @Override
            public void onError(int error) {
                String errorMessage = getErrorMessage(error);
                LOG.error("Speech recognition error: {} ({})", errorMessage, error);
                
                isRecognizing.set(false);
                if (listener != null) {
                    listener.onRecognitionError(errorMessage);
                }
            }
            
            @Override
            public void onResults(Bundle results) {
                ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                if (matches != null && !matches.isEmpty()) {
                    String recognizedText = matches.get(0);
                    LOG.info("Speech recognition result: '{}'", recognizedText);
                    
                    isRecognizing.set(false);
                    if (listener != null) {
                        listener.onRecognitionResult(recognizedText);
                    }
                } else {
                    LOG.warn("Speech recognition completed but no results");
                    isRecognizing.set(false);
                    if (listener != null) {
                        listener.onRecognitionError("未识别到语音内容");
                    }
                }
            }
            
            @Override
            public void onPartialResults(Bundle partialResults) {
                ArrayList<String> matches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                if (matches != null && !matches.isEmpty()) {
                    String partialText = matches.get(0);
                    LOG.debug("Partial recognition result: '{}'", partialText);
                }
            }
            
            @Override
            public void onEvent(int eventType, Bundle params) {
                LOG.debug("Speech recognition event: {}", eventType);
            }
        };

        speechRecognizer.setRecognitionListener(androidRecognitionListener);
    }
    
    /**
     * 开始语音识别
     */
    public boolean startRecognition() {
        if (isRecognizing.get()) {
            LOG.warn("Speech recognition already in progress");
            return false;
        }
        
        if (speechRecognizer == null) {
            LOG.error("Speech recognizer not initialized");
            return false;
        }
        
        try {
            // 创建识别意图
            android.content.Intent intent = new android.content.Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            
            // 设置语言
            String language = preferenceHelper.getVoiceInputLanguage();
            if (language == null || language.isEmpty()) {
                language = Locale.getDefault().toString();
            }
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, language);
            
            // 设置其他参数
            intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5);
            intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.getPackageName());
            
            // 设置超时
            int timeout = preferenceHelper.getVoiceInputTimeout();
            if (timeout > 0) {
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
            }
            
            // 启动识别
            speechRecognizer.startListening(intent);
            isRecognizing.set(true);
            lastAudioTime = System.currentTimeMillis();
            
            // 设置超时检查
            setupTimeoutCheck();
            
            LOG.info("Speech recognition started");
            return true;
            
        } catch (Exception e) {
            LOG.error("Failed to start speech recognition", e);
            return false;
        }
    }
    
    /**
     * 停止语音识别
     */
    public void stopRecognition() {
        if (!isRecognizing.get()) {
            return;
        }
        
        LOG.debug("Stopping speech recognition");
        
        try {
            if (speechRecognizer != null) {
                speechRecognizer.stopListening();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping speech recognition", e);
        }
        
        isRecognizing.set(false);
    }
    
    /**
     * 取消语音识别
     */
    public void cancelRecognition() {
        if (!isRecognizing.get()) {
            return;
        }
        
        LOG.debug("Cancelling speech recognition");
        
        try {
            if (speechRecognizer != null) {
                speechRecognizer.cancel();
            }
        } catch (Exception e) {
            LOG.warn("Error cancelling speech recognition", e);
        }
        
        isRecognizing.set(false);
    }
    
    /**
     * 接收音频数据（用于流式识别）
     * 注意：Android的SpeechRecognizer不直接支持流式音频输入
     * 这里主要用于监控音频活动和超时检测
     */
    public void feedAudioData(byte[] audioData, int length) {
        if (!isRecognizing.get()) {
            return;
        }
        
        // 更新最后音频时间
        lastAudioTime = System.currentTimeMillis();
        
        // 将音频数据添加到缓冲区（可用于后续处理）
        try {
            audioBuffer.write(audioData, 0, length);
        } catch (Exception e) {
            LOG.warn("Error writing audio data to buffer", e);
        }
        
        // 检查音频活动
        checkAudioActivity(audioData, length);
    }
    
    /**
     * 检查音频活动（简单的音量检测）
     */
    private void checkAudioActivity(byte[] audioData, int length) {
        // 计算音频能量（简单的RMS）
        long sum = 0;
        for (int i = 0; i < length; i += 2) {
            if (i + 1 < length) {
                short sample = (short) ((audioData[i + 1] << 8) | (audioData[i] & 0xFF));
                sum += sample * sample;
            }
        }
        
        double rms = Math.sqrt(sum / (length / 2.0));
        
        // 如果检测到足够的音频活动，重置超时
        if (rms > 1000) { // 阈值可调整
            lastAudioTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 设置超时检查
     */
    private void setupTimeoutCheck() {
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isRecognizing.get()) {
                    long currentTime = System.currentTimeMillis();
                    
                    // 检查音频超时
                    if (currentTime - lastAudioTime > AUDIO_TIMEOUT_MS) {
                        LOG.warn("Audio timeout detected");
                        stopRecognition();
                        if (listener != null) {
                            listener.onRecognitionTimeout();
                        }
                        return;
                    }
                    
                    // 检查总体超时
                    if (currentTime - lastAudioTime > RECOGNITION_TIMEOUT_MS) {
                        LOG.warn("Recognition timeout detected");
                        cancelRecognition();
                        if (listener != null) {
                            listener.onRecognitionTimeout();
                        }
                        return;
                    }
                    
                    // 继续检查
                    mainHandler.postDelayed(this, 1000);
                }
            }
        }, 1000);
    }
    
    /**
     * 获取错误信息
     */
    private String getErrorMessage(int error) {
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "音频录制错误";
            case SpeechRecognizer.ERROR_CLIENT:
                return "客户端错误";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "权限不足";
            case SpeechRecognizer.ERROR_NETWORK:
                return "网络错误";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "网络超时";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "未找到匹配结果";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "识别器忙碌";
            case SpeechRecognizer.ERROR_SERVER:
                return "服务器错误";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "语音超时";
            default:
                return "未知错误 (" + error + ")";
        }
    }
    
    /**
     * 检查是否正在识别
     */
    public boolean isRecognizing() {
        return isRecognizing.get();
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (isRecognizing.get()) {
            cancelRecognition();
        }
        
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
            speechRecognizer = null;
        }
        
        if (audioBuffer != null) {
            try {
                audioBuffer.close();
            } catch (Exception e) {
                LOG.warn("Error closing audio buffer", e);
            }
            audioBuffer = null;
        }
        
        LOG.debug("VoiceRecognitionAdapter cleaned up");
    }
}
