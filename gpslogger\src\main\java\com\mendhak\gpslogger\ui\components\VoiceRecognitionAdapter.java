/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音识别适配器 - 处理实时音频数据的语音识别
 * 支持流式音频输入，避免与录音功能的资源冲突
 */
public class VoiceRecognitionAdapter {
    private static final Logger LOG = Logs.of(VoiceRecognitionAdapter.class);
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private Handler mainHandler;
    
    // 语音识别相关
    private SpeechRecognizer speechRecognizer;
    private AtomicBoolean isRecognizing = new AtomicBoolean(false);
    private android.speech.RecognitionListener androidRecognitionListener;
    
    // 音频缓冲相关
    private java.io.ByteArrayOutputStream audioBuffer;
    private long lastAudioTime = 0;
    private static final long AUDIO_TIMEOUT_MS = 3000; // 3秒无音频则超时
    private static final long RECOGNITION_TIMEOUT_MS = 10000; // 10秒识别超时
    
    // 监听器
    private VoiceRecognitionListener listener;

    // 重试相关
    private int noMatchRetryCount = 0;
    private static final int MAX_NO_MATCH_RETRIES = 2;
    
    /**
     * 语音识别监听器接口
     */
    public interface VoiceRecognitionListener {
        void onRecognitionResult(String text);
        void onRecognitionError(String error);
        void onRecognitionTimeout();
    }
    
    /**
     * 构造函数
     */
    public VoiceRecognitionAdapter(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.audioBuffer = new java.io.ByteArrayOutputStream();
        
        initializeSpeechRecognizer();
        LOG.debug("VoiceRecognitionAdapter initialized");
    }
    
    /**
     * 设置识别监听器
     */
    public void setRecognitionListener(VoiceRecognitionListener listener) {
        this.listener = listener;
    }
    
    /**
     * 初始化语音识别器
     */
    private void initializeSpeechRecognizer() {
        // 在中国手机上，SpeechRecognizer.isRecognitionAvailable经常返回false
        // 但实际上语音识别功能可能是可用的，所以我们跳过这个检查
        boolean recognitionAvailable = SpeechRecognizer.isRecognitionAvailable(context);
        LOG.debug("SpeechRecognizer.isRecognitionAvailable: {}", recognitionAvailable);

        if (!recognitionAvailable) {
            LOG.warn("SpeechRecognizer.isRecognitionAvailable returned false, but will try to initialize anyway (common on Chinese devices)");

            // 检查是否安装了讯飞语记等中文语音识别应用
            if (checkChineseSpeechApps()) {
                LOG.info("Found Chinese speech recognition apps, proceeding with initialization");
            } else {
                LOG.warn("No Chinese speech recognition apps found, but will still try to initialize");
            }
        }

        try {
            // 首先尝试查找讯飞组件
            android.content.ComponentName xunfeiComponent = findXunfeiSpeechComponent();

            if (xunfeiComponent != null) {
                LOG.info("Found Xunfei speech component: {}, trying to create SpeechRecognizer with it", xunfeiComponent.getPackageName());
                try {
                    speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context, xunfeiComponent);
                    if (speechRecognizer != null) {
                        LOG.info("SpeechRecognizer created successfully with Xunfei component");
                        return;
                    } else {
                        LOG.warn("Failed to create SpeechRecognizer with Xunfei component, falling back to default");
                    }
                } catch (Exception e) {
                    LOG.warn("Exception creating SpeechRecognizer with Xunfei component: {}", e.getMessage());
                }
            } else {
                LOG.info("No Xunfei speech component found, will use default SpeechRecognizer");
            }

            // 回退到默认的SpeechRecognizer
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
            if (speechRecognizer == null) {
                LOG.error("Failed to create default SpeechRecognizer");
                return;
            }

            LOG.info("SpeechRecognizer created successfully with default system component");

        } catch (Exception e) {
            LOG.error("Exception creating SpeechRecognizer", e);
            return;
        }

        androidRecognitionListener = new android.speech.RecognitionListener() {
            @Override
            public void onReadyForSpeech(Bundle params) {
                LOG.debug("Speech recognizer ready for speech");
            }
            
            @Override
            public void onBeginningOfSpeech() {
                LOG.debug("Beginning of speech detected");
            }
            
            @Override
            public void onRmsChanged(float rmsdB) {
                // 音量变化，可用于UI反馈
            }
            
            @Override
            public void onBufferReceived(byte[] buffer) {
                // 接收到音频缓冲区数据
                LOG.debug("Received audio buffer: {} bytes", buffer.length);
            }
            
            @Override
            public void onEndOfSpeech() {
                LOG.debug("End of speech detected");
            }
            
            @Override
            public void onError(int error) {
                String errorMessage = getErrorMessage(error);
                LOG.error("Speech recognition error: {} ({})", errorMessage, error);

                isRecognizing.set(false);

                // 特殊处理ERROR_NO_MATCH错误
                if (error == SpeechRecognizer.ERROR_NO_MATCH) {
                    LOG.warn("ERROR_NO_MATCH detected - this often indicates the speech recognition service is not properly configured for Chinese");

                    // 尝试重新初始化语音识别器
                    if (shouldRetryOnNoMatch()) {
                        LOG.info("Attempting to reinitialize speech recognizer for Chinese language support");
                        reinitializeForChinese();
                        return;
                    }
                }

                if (listener != null) {
                    listener.onRecognitionError(errorMessage);
                }
            }
            
            @Override
            public void onResults(Bundle results) {
                ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                if (matches != null && !matches.isEmpty()) {
                    String recognizedText = matches.get(0);
                    LOG.info("Speech recognition result: '{}'", recognizedText);
                    
                    isRecognizing.set(false);
                    if (listener != null) {
                        listener.onRecognitionResult(recognizedText);
                    }
                } else {
                    LOG.warn("Speech recognition completed but no results");
                    isRecognizing.set(false);
                    if (listener != null) {
                        listener.onRecognitionError("未识别到语音内容");
                    }
                }
            }
            
            @Override
            public void onPartialResults(Bundle partialResults) {
                ArrayList<String> matches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                if (matches != null && !matches.isEmpty()) {
                    String partialText = matches.get(0);
                    LOG.debug("Partial recognition result: '{}'", partialText);
                }
            }
            
            @Override
            public void onEvent(int eventType, Bundle params) {
                LOG.debug("Speech recognition event: {}", eventType);
            }
        };

        speechRecognizer.setRecognitionListener(androidRecognitionListener);
    }
    
    /**
     * 开始语音识别
     */
    public boolean startRecognition() {
        if (isRecognizing.get()) {
            LOG.warn("Speech recognition already in progress");
            return false;
        }

        // 首先尝试直接调用讯飞语记
        if (tryDirectXunfeiRecognition()) {
            LOG.info("Using direct Xunfei recognition");
            return true;
        }

        // 如果直接调用失败，回退到系统SpeechRecognizer
        LOG.info("Direct Xunfei recognition not available, using system SpeechRecognizer");

        if (speechRecognizer == null) {
            LOG.error("Speech recognizer not initialized, attempting to reinitialize");
            initializeSpeechRecognizer();

            if (speechRecognizer == null) {
                LOG.error("Failed to reinitialize speech recognizer");
                if (listener != null) {
                    listener.onRecognitionError("语音识别组件初始化失败，请确认已安装讯飞语记等语音识别应用");
                }
                return false;
            }
        }
        
        try {
            // 创建识别意图
            android.content.Intent intent = new android.content.Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

            // 设置语言 - 对中文进行特殊处理
            String language = preferenceHelper.getVoiceInputLanguage();
            if (language == null || language.isEmpty()) {
                language = Locale.getDefault().toString();
            }

            // 确保中文语言设置正确
            if (language.startsWith("zh")) {
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN");
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, "zh-CN");
                LOG.debug("Set language to zh-CN for Chinese recognition");
            } else {
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, language);
            }

            // 设置其他参数
            intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5);
            intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.getPackageName());
            intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话");

            // 设置超时
            int timeout = preferenceHelper.getVoiceInputTimeout();
            if (timeout > 0) {
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, timeout * 1000L);
            } else {
                // 设置默认超时（对中文语音识别很重要）
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 3000L);
                intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 3000L);
            }

            // 添加中文语音识别的优化参数
            addChineseSpeechOptimizations(intent);

            // 添加讯飞语记的特殊参数（如果可用）
            android.content.ComponentName xunfeiComponent = findXunfeiSpeechComponent();
            if (xunfeiComponent != null) {
                LOG.info("Using Xunfei component for speech recognition: {}", xunfeiComponent.getPackageName());
                // 添加讯飞特有的参数
                intent.putExtra("engine", "xunfei");
                intent.putExtra("domain", "general");
                intent.putExtra("accent", "mandarin");
                intent.putExtra("language", "zh_cn");
            } else {
                LOG.info("No specific Xunfei component, using system default with Chinese optimizations");
                // 为系统默认识别器添加中文优化
                intent.putExtra("android.speech.extra.EXTRA_ADDITIONAL_LANGUAGES", new String[]{"zh-CN", "zh-TW", "zh-HK"});
            }
            
            // 启动识别
            LOG.debug("Starting speech recognition with intent: {}", intent.getExtras());
            speechRecognizer.startListening(intent);
            isRecognizing.set(true);
            lastAudioTime = System.currentTimeMillis();

            // 设置超时检查
            setupTimeoutCheck();

            // 重置重试计数（成功启动）
            noMatchRetryCount = 0;

            LOG.info("Speech recognition started successfully");
            return true;

        } catch (SecurityException e) {
            LOG.error("Security exception starting speech recognition - permission issue", e);
            if (listener != null) {
                listener.onRecognitionError("语音识别权限不足，请检查应用权限设置");
            }
            return false;
        } catch (IllegalStateException e) {
            LOG.error("Illegal state exception starting speech recognition", e);
            if (listener != null) {
                listener.onRecognitionError("语音识别状态错误，请重试");
            }
            return false;
        } catch (Exception e) {
            LOG.error("Failed to start speech recognition", e);
            if (listener != null) {
                String errorMsg = "启动语音识别失败";
                if (e.getMessage() != null) {
                    errorMsg += ": " + e.getMessage();
                }
                listener.onRecognitionError(errorMsg);
            }
            return false;
        }
    }
    
    /**
     * 停止语音识别
     */
    public void stopRecognition() {
        if (!isRecognizing.get()) {
            return;
        }
        
        LOG.debug("Stopping speech recognition");
        
        try {
            if (speechRecognizer != null) {
                speechRecognizer.stopListening();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping speech recognition", e);
        }
        
        isRecognizing.set(false);
    }
    
    /**
     * 取消语音识别
     */
    public void cancelRecognition() {
        if (!isRecognizing.get()) {
            return;
        }
        
        LOG.debug("Cancelling speech recognition");
        
        try {
            if (speechRecognizer != null) {
                speechRecognizer.cancel();
            }
        } catch (Exception e) {
            LOG.warn("Error cancelling speech recognition", e);
        }
        
        isRecognizing.set(false);
    }
    
    /**
     * 接收音频数据（用于流式识别）
     * 注意：Android的SpeechRecognizer不直接支持流式音频输入
     * 这里主要用于监控音频活动和超时检测
     */
    public void feedAudioData(byte[] audioData, int length) {
        if (!isRecognizing.get()) {
            return;
        }
        
        // 更新最后音频时间
        lastAudioTime = System.currentTimeMillis();
        
        // 将音频数据添加到缓冲区（可用于后续处理）
        try {
            audioBuffer.write(audioData, 0, length);
        } catch (Exception e) {
            LOG.warn("Error writing audio data to buffer", e);
        }
        
        // 检查音频活动
        checkAudioActivity(audioData, length);
    }
    
    /**
     * 检查音频活动（简单的音量检测）
     */
    private void checkAudioActivity(byte[] audioData, int length) {
        // 计算音频能量（简单的RMS）
        long sum = 0;
        for (int i = 0; i < length; i += 2) {
            if (i + 1 < length) {
                short sample = (short) ((audioData[i + 1] << 8) | (audioData[i] & 0xFF));
                sum += sample * sample;
            }
        }
        
        double rms = Math.sqrt(sum / (length / 2.0));
        
        // 如果检测到足够的音频活动，重置超时
        if (rms > 1000) { // 阈值可调整
            lastAudioTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 设置超时检查
     */
    private void setupTimeoutCheck() {
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isRecognizing.get()) {
                    long currentTime = System.currentTimeMillis();
                    
                    // 检查音频超时
                    if (currentTime - lastAudioTime > AUDIO_TIMEOUT_MS) {
                        LOG.warn("Audio timeout detected");
                        stopRecognition();
                        if (listener != null) {
                            listener.onRecognitionTimeout();
                        }
                        return;
                    }
                    
                    // 检查总体超时
                    if (currentTime - lastAudioTime > RECOGNITION_TIMEOUT_MS) {
                        LOG.warn("Recognition timeout detected");
                        cancelRecognition();
                        if (listener != null) {
                            listener.onRecognitionTimeout();
                        }
                        return;
                    }
                    
                    // 继续检查
                    mainHandler.postDelayed(this, 1000);
                }
            }
        }, 1000);
    }
    
    /**
     * 获取错误信息
     */
    private String getErrorMessage(int error) {
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "音频录制错误，请检查麦克风权限";
            case SpeechRecognizer.ERROR_CLIENT:
                return "客户端错误，请重试";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "权限不足，请授予录音权限";
            case SpeechRecognizer.ERROR_NETWORK:
                return "网络错误，请检查网络连接";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "网络超时，请检查网络连接";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "未识别到语音内容，请重新说话";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "语音识别器忙碌，请稍后重试";
            case SpeechRecognizer.ERROR_SERVER:
                return "语音识别服务器错误，请重试";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "语音输入超时，请重新说话";
            default:
                return "语音识别错误 (代码: " + error + ")，请确认已安装讯飞语记等语音识别应用";
        }
    }
    
    /**
     * 检查是否正在识别
     */
    public boolean isRecognizing() {
        return isRecognizing.get();
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (isRecognizing.get()) {
            cancelRecognition();
        }
        
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
            speechRecognizer = null;
        }
        
        if (audioBuffer != null) {
            try {
                audioBuffer.close();
            } catch (Exception e) {
                LOG.warn("Error closing audio buffer", e);
            }
            audioBuffer = null;
        }
        
        LOG.debug("VoiceRecognitionAdapter cleaned up");
    }

    /**
     * 检查是否安装了中文语音识别应用
     */
    private boolean checkChineseSpeechApps() {
        android.content.pm.PackageManager pm = context.getPackageManager();
        String[] chineseSpeechApps = {
            "com.iflytek.speechcloud",      // 讯飞语记
            "com.iflytek.inputmethod",      // 讯飞输入法
            "com.iflytek.vflynote",         // 讯飞语记（另一个包名）
            "com.baidu.input",              // 百度输入法
            "com.sohu.inputmethod.sogou",   // 搜狗输入法
            "com.tencent.qqpinyin",         // QQ输入法
            "com.huawei.vassistant",        // 华为语音助手
            "com.xiaomi.voiceassistant",    // 小米语音助手
            "com.oppo.breeno",              // OPPO Breeno
            "com.vivo.voice"                // vivo语音助手
        };

        for (String packageName : chineseSpeechApps) {
            try {
                pm.getPackageInfo(packageName, 0);
                LOG.info("Found Chinese speech app: {}", packageName);
                return true;
            } catch (android.content.pm.PackageManager.NameNotFoundException e) {
                // 应用未安装，继续检查下一个
            }
        }

        LOG.debug("No Chinese speech apps found in predefined list");
        return false;
    }

    /**
     * 查找讯飞语记的语音识别组件
     */
    private android.content.ComponentName findXunfeiSpeechComponent() {
        android.content.pm.PackageManager pm = context.getPackageManager();

        // 方法1：查找语音识别服务
        android.content.ComponentName serviceComponent = findSpeechRecognitionServices(pm);
        if (serviceComponent != null) {
            return serviceComponent;
        }

        // 方法2：查找语音识别Activity
        android.content.ComponentName activityComponent = findSpeechRecognitionActivities(pm);
        if (activityComponent != null) {
            return activityComponent;
        }

        // 方法3：尝试直接构造已知的讯飞组件
        android.content.ComponentName knownComponent = tryKnownXunfeiComponents(pm);
        if (knownComponent != null) {
            return knownComponent;
        }

        // 方法4：查找所有讯飞相关的组件
        android.content.ComponentName anyXunfeiComponent = findAnyXunfeiComponent(pm);
        if (anyXunfeiComponent != null) {
            return anyXunfeiComponent;
        }

        LOG.warn("No Xunfei speech component found after exhaustive search");
        return null;
    }

    /**
     * 查找语音识别服务
     */
    private android.content.ComponentName findSpeechRecognitionServices(android.content.pm.PackageManager pm) {
        android.content.Intent intent = new android.content.Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        java.util.List<android.content.pm.ResolveInfo> resolveInfos = pm.queryIntentServices(intent, 0);

        LOG.debug("Found {} speech recognition services", resolveInfos.size());

        for (android.content.pm.ResolveInfo resolveInfo : resolveInfos) {
            String packageName = resolveInfo.serviceInfo.packageName;
            String serviceName = resolveInfo.serviceInfo.name;

            LOG.debug("Found speech service: {} / {}", packageName, serviceName);

            // 检查是否是讯飞相关的服务
            if (packageName.contains("iflytek") || packageName.contains("xunfei")) {
                LOG.info("Found Xunfei speech service: {} / {}", packageName, serviceName);
                return new android.content.ComponentName(packageName, serviceName);
            }
        }

        return null;
    }

    /**
     * 查找语音识别Activity
     */
    private android.content.ComponentName findSpeechRecognitionActivities(android.content.pm.PackageManager pm) {
        android.content.Intent intent = new android.content.Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        java.util.List<android.content.pm.ResolveInfo> activityInfos = pm.queryIntentActivities(intent, 0);

        LOG.debug("Found {} speech recognition activities", activityInfos.size());

        for (android.content.pm.ResolveInfo resolveInfo : activityInfos) {
            String packageName = resolveInfo.activityInfo.packageName;
            String activityName = resolveInfo.activityInfo.name;

            LOG.debug("Found speech activity: {} / {}", packageName, activityName);

            // 检查是否是讯飞相关的Activity
            if (packageName.contains("iflytek") || packageName.contains("xunfei")) {
                LOG.info("Found Xunfei speech activity: {} / {}", packageName, activityName);
                return new android.content.ComponentName(packageName, activityName);
            }
        }

        return null;
    }

    /**
     * 尝试已知的讯飞组件
     */
    private android.content.ComponentName tryKnownXunfeiComponents(android.content.pm.PackageManager pm) {
        String[] knownXunfeiComponents = {
            "com.iflytek.vflynote/.speech.SpeechRecognitionService",
            "com.iflytek.vflynote/.recognition.RecognitionService",
            "com.iflytek.speechcloud/.speech.SpeechRecognitionService",
            "com.iflytek.speechcloud/.recognition.RecognitionService",
            "com.iflytek.inputmethod/.speech.SpeechRecognitionService",
            "com.iflytek.inputmethod/.recognition.RecognitionService"
        };

        for (String componentString : knownXunfeiComponents) {
            try {
                android.content.ComponentName component = android.content.ComponentName.unflattenFromString(componentString);
                if (component != null) {
                    // 检查组件是否存在
                    try {
                        pm.getServiceInfo(component, 0);
                        LOG.info("Found known Xunfei service component: {}", componentString);
                        return component;
                    } catch (android.content.pm.PackageManager.NameNotFoundException e) {
                        // 尝试作为Activity
                        try {
                            pm.getActivityInfo(component, 0);
                            LOG.info("Found known Xunfei activity component: {}", componentString);
                            return component;
                        } catch (android.content.pm.PackageManager.NameNotFoundException e2) {
                            // 组件不存在，继续尝试下一个
                        }
                    }
                }
            } catch (Exception e) {
                LOG.debug("Failed to check component {}: {}", componentString, e.getMessage());
            }
        }

        return null;
    }

    /**
     * 查找任何讯飞相关的组件
     */
    private android.content.ComponentName findAnyXunfeiComponent(android.content.pm.PackageManager pm) {
        // 获取所有讯飞相关的包
        java.util.List<android.content.pm.PackageInfo> packages = pm.getInstalledPackages(android.content.pm.PackageManager.GET_SERVICES | android.content.pm.PackageManager.GET_ACTIVITIES);

        for (android.content.pm.PackageInfo packageInfo : packages) {
            String packageName = packageInfo.packageName;

            if (packageName.contains("iflytek") || packageName.contains("xunfei")) {
                LOG.debug("Examining Xunfei package: {}", packageName);

                // 检查服务
                if (packageInfo.services != null) {
                    for (android.content.pm.ServiceInfo serviceInfo : packageInfo.services) {
                        LOG.debug("Found service in {}: {}", packageName, serviceInfo.name);
                        if (serviceInfo.name.toLowerCase().contains("speech") ||
                            serviceInfo.name.toLowerCase().contains("recognition") ||
                            serviceInfo.name.toLowerCase().contains("voice")) {
                            LOG.info("Found potential Xunfei speech service: {} / {}", packageName, serviceInfo.name);
                            return new android.content.ComponentName(packageName, serviceInfo.name);
                        }
                    }
                }

                // 检查Activity
                if (packageInfo.activities != null) {
                    for (android.content.pm.ActivityInfo activityInfo : packageInfo.activities) {
                        LOG.debug("Found activity in {}: {}", packageName, activityInfo.name);
                        if (activityInfo.name.toLowerCase().contains("speech") ||
                            activityInfo.name.toLowerCase().contains("recognition") ||
                            activityInfo.name.toLowerCase().contains("voice")) {
                            LOG.info("Found potential Xunfei speech activity: {} / {}", packageName, activityInfo.name);
                            return new android.content.ComponentName(packageName, activityInfo.name);
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 添加中文语音识别优化参数
     */
    private void addChineseSpeechOptimizations(android.content.Intent intent) {
        // 设置中文相关的优化参数
        intent.putExtra("android.speech.extra.DICTATION_MODE", true);
        intent.putExtra("android.speech.extra.PARTIAL_RESULTS", true);
        intent.putExtra("android.speech.extra.GET_AUDIO_FORMAT", "audio/wav");
        intent.putExtra("android.speech.extra.GET_AUDIO", true);

        // 设置识别模式为自由说话模式（适合中文）
        intent.putExtra("android.speech.extra.SPEECH_INPUT_MINIMUM_LENGTH_MILLIS", 1000);
        intent.putExtra("android.speech.extra.SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS", 2000);
        intent.putExtra("android.speech.extra.SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS", 2000);

        // 添加中文语言变体
        intent.putExtra("android.speech.extra.SUPPORTED_LANGUAGES", new String[]{
            "zh-CN", "zh-TW", "zh-HK", "zh", "cmn-Hans-CN", "cmn-Hant-TW"
        });

        // 设置识别引擎偏好
        intent.putExtra("android.speech.extra.PREFER_OFFLINE", false); // 在线识别通常更准确
        intent.putExtra("android.speech.extra.ONLY_RETURN_LANGUAGE_THAT_CAN_BE_USED_UNAMBIGUOUSLY", false);

        LOG.debug("Added Chinese speech recognition optimizations to intent");
    }

    /**
     * 检查语音识别是否真正可用
     */
    public boolean isSpeechRecognitionReallyAvailable() {
        // 方法1：检查系统API
        boolean systemAvailable = SpeechRecognizer.isRecognitionAvailable(context);

        // 方法2：检查是否安装了中文语音识别应用
        boolean chineseAppsAvailable = checkChineseSpeechApps();

        // 方法3：检查是否能找到语音识别组件
        android.content.ComponentName component = findXunfeiSpeechComponent();
        boolean componentAvailable = (component != null);

        // 方法4：尝试创建SpeechRecognizer
        boolean canCreateRecognizer = false;
        try {
            SpeechRecognizer testRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
            if (testRecognizer != null) {
                canCreateRecognizer = true;
                testRecognizer.destroy();
            }
        } catch (Exception e) {
            LOG.debug("Cannot create test SpeechRecognizer: {}", e.getMessage());
        }

        LOG.info("Speech recognition availability check:");
        LOG.info("  System API: {}", systemAvailable);
        LOG.info("  Chinese apps: {}", chineseAppsAvailable);
        LOG.info("  Component found: {}", componentAvailable);
        LOG.info("  Can create recognizer: {}", canCreateRecognizer);

        // 如果任何一种方法表明可用，就认为可用
        boolean available = systemAvailable || chineseAppsAvailable || componentAvailable || canCreateRecognizer;

        LOG.info("Final speech recognition availability: {}", available);
        return available;
    }

    /**
     * 检查是否应该在ERROR_NO_MATCH时重试
     */
    private boolean shouldRetryOnNoMatch() {
        return noMatchRetryCount < MAX_NO_MATCH_RETRIES;
    }

    /**
     * 为中文语音识别重新初始化
     */
    private void reinitializeForChinese() {
        noMatchRetryCount++;
        LOG.info("Reinitializing speech recognizer for Chinese (attempt {})", noMatchRetryCount);

        // 清理当前的识别器
        if (speechRecognizer != null) {
            try {
                speechRecognizer.destroy();
            } catch (Exception e) {
                LOG.warn("Error destroying speech recognizer: {}", e.getMessage());
            }
            speechRecognizer = null;
        }

        // 重新初始化
        initializeSpeechRecognizer();

        // 如果初始化成功，尝试重新开始识别
        if (speechRecognizer != null) {
            LOG.info("Speech recognizer reinitialized, attempting to restart recognition");

            // 延迟一点时间再重新开始，避免过快重试
            mainHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (!isRecognizing.get()) {
                        LOG.info("Restarting speech recognition after reinitialization");
                        startRecognition();
                    }
                }
            }, 500); // 延迟500ms
        } else {
            LOG.error("Failed to reinitialize speech recognizer");
            if (listener != null) {
                listener.onRecognitionError("语音识别重新初始化失败，请确认已安装讯飞语记等语音识别应用");
            }
        }
    }

    /**
     * 重置重试计数
     */
    public void resetRetryCount() {
        noMatchRetryCount = 0;
        LOG.debug("Reset retry count");
    }

    /**
     * 尝试直接调用讯飞语记进行语音识别
     */
    private boolean tryDirectXunfeiRecognition() {
        try {
            // 检查讯飞语记是否已安装
            android.content.pm.PackageManager pm = context.getPackageManager();
            String[] xunfeiPackages = {
                "com.iflytek.vflynote",
                "com.iflytek.speechcloud",
                "com.iflytek.inputmethod"
            };

            String installedPackage = null;
            for (String packageName : xunfeiPackages) {
                try {
                    pm.getPackageInfo(packageName, 0);
                    installedPackage = packageName;
                    LOG.info("Found installed Xunfei package: {}", packageName);
                    break;
                } catch (android.content.pm.PackageManager.NameNotFoundException e) {
                    // 继续检查下一个包
                }
            }

            if (installedPackage == null) {
                LOG.warn("No Xunfei packages found for direct recognition");
                return false;
            }

            // 创建直接调用讯飞语记的Intent
            android.content.Intent intent = createDirectXunfeiIntent(installedPackage);
            if (intent == null) {
                LOG.warn("Failed to create direct Xunfei intent");
                return false;
            }

            // 启动讯飞语记进行语音识别
            if (context instanceof android.app.Activity) {
                android.app.Activity activity = (android.app.Activity) context;
                try {
                    isRecognizing.set(true);
                    activity.startActivityForResult(intent, XUNFEI_RECOGNITION_REQUEST_CODE);
                    LOG.info("Started direct Xunfei recognition with package: {}", installedPackage);
                    return true;
                } catch (Exception e) {
                    LOG.error("Failed to start direct Xunfei recognition: {}", e.getMessage());
                    isRecognizing.set(false);
                }
            } else {
                LOG.error("Context is not an Activity, cannot start direct Xunfei recognition");
            }

        } catch (Exception e) {
            LOG.error("Error in tryDirectXunfeiRecognition: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 创建直接调用讯飞语记的Intent
     */
    private android.content.Intent createDirectXunfeiIntent(String packageName) {
        try {
            // 方法1：尝试使用标准的语音识别Intent
            android.content.Intent intent = new android.content.Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            intent.setPackage(packageName);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN");
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, "zh-CN");
            intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.getPackageName());
            intent.putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话...");
            intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);
            intent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);

            // 添加讯飞特有参数
            intent.putExtra("engine", "xunfei");
            intent.putExtra("domain", "general");
            intent.putExtra("accent", "mandarin");
            intent.putExtra("language", "zh_cn");

            // 检查是否有应用能处理这个Intent
            android.content.pm.PackageManager pm = context.getPackageManager();
            if (pm.resolveActivity(intent, 0) != null) {
                LOG.info("Created standard speech recognition intent for package: {}", packageName);
                return intent;
            }

            // 方法2：尝试直接启动讯飞语记的主Activity
            intent = pm.getLaunchIntentForPackage(packageName);
            if (intent != null) {
                intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("action", "voice_recognition");
                intent.putExtra("language", "zh-CN");
                LOG.info("Created launch intent for package: {}", packageName);
                return intent;
            }

            LOG.warn("No suitable intent found for package: {}", packageName);
            return null;

        } catch (Exception e) {
            LOG.error("Error creating direct Xunfei intent: {}", e.getMessage());
            return null;
        }
    }

    // 请求代码常量
    private static final int XUNFEI_RECOGNITION_REQUEST_CODE = 1002;

    /**
     * 处理直接调用讯飞语记的Activity结果
     */
    public void handleActivityResult(int requestCode, int resultCode, android.content.Intent data) {
        if (requestCode == XUNFEI_RECOGNITION_REQUEST_CODE) {
            isRecognizing.set(false);

            if (resultCode == android.app.Activity.RESULT_OK && data != null) {
                // 尝试从标准的语音识别结果中获取文本
                java.util.ArrayList<String> results = data.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS);
                if (results != null && !results.isEmpty()) {
                    String recognizedText = results.get(0);
                    LOG.info("Direct Xunfei recognition result: '{}'", recognizedText);

                    if (listener != null) {
                        listener.onRecognitionResult(recognizedText);
                    }
                    return;
                }

                // 尝试从其他可能的字段获取结果
                String text = data.getStringExtra("recognition_result");
                if (text == null) {
                    text = data.getStringExtra("result");
                }
                if (text == null) {
                    text = data.getStringExtra("text");
                }

                if (text != null && !text.trim().isEmpty()) {
                    LOG.info("Direct Xunfei recognition result from alternative field: '{}'", text);
                    if (listener != null) {
                        listener.onRecognitionResult(text);
                    }
                    return;
                }

                LOG.warn("Direct Xunfei recognition returned OK but no text found");
                if (listener != null) {
                    listener.onRecognitionError("语音识别成功但未获取到文本内容");
                }

            } else if (resultCode == android.app.Activity.RESULT_CANCELED) {
                LOG.info("Direct Xunfei recognition was cancelled by user");
                if (listener != null) {
                    listener.onRecognitionError("语音识别已取消");
                }
            } else {
                LOG.warn("Direct Xunfei recognition failed with result code: {}", resultCode);
                if (listener != null) {
                    listener.onRecognitionError("语音识别失败，请重试");
                }
            }
        }
    }

    /**
     * 获取直接调用讯飞语记的请求代码
     */
    public static int getXunfeiRequestCode() {
        return XUNFEI_RECOGNITION_REQUEST_CODE;
    }
}
