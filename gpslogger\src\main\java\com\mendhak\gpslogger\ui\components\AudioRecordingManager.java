package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.media.MediaRecorder;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.loggers.Files;

import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Pattern;

/**
 * Manages audio recording for voice input annotations
 * Records user's voice input simultaneously with speech recognition
 */
public class AudioRecordingManager {
    private static final Logger LOG = Logs.of(AudioRecordingManager.class);
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private MediaRecorder mediaRecorder;
    private boolean isRecording = false;
    private String currentRecordingPath;
    private AudioRecordingListener listener;
    private Handler mainHandler;
    private Runnable timeoutRunnable;

    // Recording timeout (5 minutes max)
    private static final long RECORDING_TIMEOUT_MS = 5 * 60 * 1000;
    
    // Audio recording settings - optimized for concurrent recording
    private static final int PRIMARY_AUDIO_SOURCE = MediaRecorder.AudioSource.VOICE_RECOGNITION;
    private static final int FALLBACK_AUDIO_SOURCE = MediaRecorder.AudioSource.MIC;
    private static final int OUTPUT_FORMAT = MediaRecorder.OutputFormat.THREE_GPP;
    private static final int AUDIO_ENCODER = MediaRecorder.AudioEncoder.AMR_NB;
    private static final int SAMPLE_RATE = 8000; // Lower sample rate for compatibility
    private static final int BIT_RATE = 12200; // Standard AMR-NB bitrate
    
    // File naming constants
    private static final Pattern INVALID_FILENAME_CHARS = Pattern.compile("[\\\\/:*?\"<>|]");
    private static final int MAX_FILENAME_LENGTH = 200;
    
    /**
     * Interface for audio recording events
     */
    public interface AudioRecordingListener {
        void onRecordingStarted(String filePath);
        void onRecordingStopped(String filePath);
        void onRecordingError(String error);
        void onRecordingStatusUpdate(String status); // New callback for status updates
    }
    
    /**
     * Constructor
     */
    public AudioRecordingManager(Context context, AudioRecordingListener listener) {
        System.out.println("=== AudioRecordingManager: Constructor called ===");
        this.context = context;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        LOG.debug("AudioRecordingManager initialized");
        System.out.println("=== AudioRecordingManager: Initialization complete ===");
    }
    
    /**
     * Start audio recording
     * @return true if recording started successfully, false otherwise
     */
    public boolean startRecording() {
        if (isRecording) {
            LOG.warn("Recording is already in progress");
            return false;
        }

        LOG.info("=== STARTING AUDIO RECORDING ===");

        // Pre-flight checks
        if (!performPreflightChecks()) {
            return false;
        }

        try {
            // Generate temporary file path
            String tempFileName = generateTempFileName();
            String gpsLoggerFolder = preferenceHelper.getGpsLoggerFolder();
            File recordingFile = new File(gpsLoggerFolder, tempFileName);
            currentRecordingPath = recordingFile.getAbsolutePath();
            
            LOG.debug("Recording to temporary file: {}", currentRecordingPath);
            
            // Ensure directory exists
            File parentDir = recordingFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                LOG.debug("Created directory: {} - {}", parentDir.getAbsolutePath(), created);
            }
            
            // Initialize MediaRecorder with fallback strategy
            mediaRecorder = configureMediaRecorder(currentRecordingPath);
            
            // Start recording (MediaRecorder is already prepared in configureMediaRecorder)
            LOG.debug("Starting MediaRecorder...");
            mediaRecorder.start();

            isRecording = true;
            LOG.info("Audio recording started successfully: {}", currentRecordingPath);

            // Verify recording is actually working
            verifyRecordingStatus();

            // Set up timeout to prevent runaway recordings
            setupRecordingTimeout();

            // Notify listener on main thread
            if (listener != null) {
                mainHandler.post(() -> listener.onRecordingStarted(currentRecordingPath));
            }

            return true;
            
        } catch (IOException e) {
            LOG.error("Failed to start audio recording - IOException: {}", e.getMessage(), e);
            cleanup();
            notifyError("录音启动失败(IO错误): " + e.getMessage());
            return false;
        } catch (IllegalStateException e) {
            LOG.error("Failed to start audio recording - IllegalStateException: {}", e.getMessage(), e);
            cleanup();
            notifyError("录音状态错误: " + e.getMessage());
            return false;
        } catch (RuntimeException e) {
            LOG.error("Failed to start audio recording - RuntimeException: {}", e.getMessage(), e);
            cleanup();
            notifyError("录音运行时错误: " + e.getMessage());
            return false;
        } catch (Exception e) {
            LOG.error("Failed to start audio recording - Unexpected error: {}", e.getMessage(), e);
            cleanup();
            notifyError("录音启动失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Stop audio recording and save with final filename
     * @param finalAnnotationText The processed annotation text to use as filename
     * @return The final file path if successful, null otherwise
     */
    public String stopRecording(String finalAnnotationText) {
        if (!isRecording) {
            LOG.warn("No recording in progress");
            return null;
        }
        
        LOG.info("=== STOPPING AUDIO RECORDING ===");
        LOG.debug("Final annotation text for filename: '{}'", finalAnnotationText);
        
        try {
            // Stop MediaRecorder
            if (mediaRecorder != null) {
                mediaRecorder.stop();
                mediaRecorder.release();
                mediaRecorder = null;
            }
            
            isRecording = false;
            
            // Generate final filename based on annotation text
            String finalFileName = generateFinalFileName(finalAnnotationText);
            String gpsLoggerFolder = preferenceHelper.getGpsLoggerFolder();
            File finalFile = new File(gpsLoggerFolder, finalFileName);
            String finalPath = finalFile.getAbsolutePath();
            
            // Move/rename temporary file to final location
            File tempFile = new File(currentRecordingPath);
            if (tempFile.exists()) {
                boolean renamed = tempFile.renameTo(finalFile);
                if (renamed) {
                    LOG.info("Audio recording saved successfully: {}", finalPath);
                    
                    // Notify listener on main thread
                    if (listener != null) {
                        mainHandler.post(() -> listener.onRecordingStopped(finalPath));
                    }
                    
                    return finalPath;
                } else {
                    LOG.error("Failed to rename recording file from {} to {}", currentRecordingPath, finalPath);
                    notifyError("保存录音文件失败");
                    return null;
                }
            } else {
                LOG.error("Temporary recording file not found: {}", currentRecordingPath);
                notifyError("录音文件丢失");
                return null;
            }
            
        } catch (IllegalStateException e) {
            LOG.error("Failed to stop audio recording - IllegalStateException", e);
            cleanup();
            notifyError("停止录音失败: " + e.getMessage());
            return null;
        } catch (Exception e) {
            LOG.error("Failed to stop audio recording - Unexpected error", e);
            cleanup();
            notifyError("停止录音失败: " + e.getMessage());
            return null;
        } finally {
            currentRecordingPath = null;
            // Clear timeout
            clearRecordingTimeout();
        }
    }
    
    /**
     * Cancel current recording without saving
     */
    public void cancelRecording() {
        if (!isRecording) {
            return;
        }
        
        LOG.info("Cancelling audio recording");
        
        try {
            if (mediaRecorder != null) {
                mediaRecorder.stop();
                mediaRecorder.release();
                mediaRecorder = null;
            }
        } catch (Exception e) {
            LOG.warn("Error stopping MediaRecorder during cancel", e);
        }
        
        // Delete temporary file
        if (currentRecordingPath != null) {
            File tempFile = new File(currentRecordingPath);
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                LOG.debug("Deleted temporary recording file: {} - {}", currentRecordingPath, deleted);
            }
        }
        
        isRecording = false;
        currentRecordingPath = null;

        // Clear timeout
        clearRecordingTimeout();

        LOG.debug("Audio recording cancelled");
    }
    
    /**
     * Check if currently recording
     */
    public boolean isRecording() {
        return isRecording;
    }
    
    /**
     * Get current recording file path
     */
    public String getCurrentRecordingPath() {
        return currentRecordingPath;
    }
    
    /**
     * Set the audio recording listener
     */
    public void setListener(AudioRecordingListener listener) {
        this.listener = listener;
    }

    /**
     * Generate temporary filename for recording
     */
    private String generateTempFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss_SSS", Locale.getDefault());
        String timestamp = sdf.format(new Date());
        return "voice_recording_temp_" + timestamp + ".3gp";
    }

    /**
     * Generate final filename based on annotation text
     */
    private String generateFinalFileName(String annotationText) {
        if (annotationText == null || annotationText.trim().isEmpty()) {
            // Fallback to timestamp if no annotation text
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
            String timestamp = sdf.format(new Date());
            return "voice_annotation_" + timestamp + ".wav";
        }

        // Clean the annotation text for use as filename
        String cleanText = sanitizeFileName(annotationText.trim());

        // Limit filename length
        if (cleanText.length() > MAX_FILENAME_LENGTH) {
            cleanText = cleanText.substring(0, MAX_FILENAME_LENGTH);
        }

        // Ensure filename is not empty after cleaning
        if (cleanText.isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
            String timestamp = sdf.format(new Date());
            return "voice_annotation_" + timestamp + ".3gp";
        }

        return cleanText + ".3gp";
    }

    /**
     * Sanitize text for use as filename
     */
    private String sanitizeFileName(String text) {
        if (text == null) {
            return "";
        }

        // Replace invalid characters with underscore
        String sanitized = INVALID_FILENAME_CHARS.matcher(text).replaceAll("_");

        // Replace multiple consecutive underscores with single underscore
        sanitized = sanitized.replaceAll("_{2,}", "_");

        // Remove leading and trailing underscores
        sanitized = sanitized.replaceAll("^_+|_+$", "");

        return sanitized;
    }

    /**
     * Cleanup resources
     */
    private void cleanup() {
        try {
            if (mediaRecorder != null) {
                mediaRecorder.release();
                mediaRecorder = null;
            }
        } catch (Exception e) {
            LOG.warn("Error during cleanup", e);
        }

        isRecording = false;

        // Clear timeout
        clearRecordingTimeout();

        // Delete temporary file if exists
        if (currentRecordingPath != null) {
            File tempFile = new File(currentRecordingPath);
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                LOG.debug("Cleanup: deleted temporary file {} - {}", currentRecordingPath, deleted);
            }
            currentRecordingPath = null;
        }
    }

    /**
     * Notify error on main thread
     */
    private void notifyError(String error) {
        if (listener != null) {
            mainHandler.post(() -> listener.onRecordingError(error));
        }
    }

    /**
     * Notify status update on main thread
     */
    private void notifyStatusUpdate(String status) {
        if (listener != null) {
            mainHandler.post(() -> listener.onRecordingStatusUpdate(status));
        }
    }

    /**
     * Check if audio recording is supported on this device
     */
    public static boolean isAudioRecordingSupported() {
        return testAudioSource(PRIMARY_AUDIO_SOURCE) || testAudioSource(FALLBACK_AUDIO_SOURCE);
    }

    /**
     * Test if a specific audio source is supported
     */
    private static boolean testAudioSource(int audioSource) {
        try {
            LOG.debug("Testing audio source: {}", audioSource);
            MediaRecorder testRecorder = new MediaRecorder();
            testRecorder.setAudioSource(audioSource);
            testRecorder.setOutputFormat(OUTPUT_FORMAT);
            testRecorder.setAudioEncoder(AUDIO_ENCODER);
            testRecorder.release();
            LOG.debug("Audio source {} is supported", audioSource);
            return true;
        } catch (Exception e) {
            LOG.debug("Audio source {} not supported: {}", audioSource, e.getMessage());
            return false;
        }
    }

    /**
     * Get detailed audio recording capabilities
     */
    public static String getAudioRecordingCapabilities() {
        StringBuilder capabilities = new StringBuilder();
        capabilities.append("Audio Recording Capabilities:\n");

        // Test VOICE_RECOGNITION source
        boolean voiceRecSupported = testAudioSource(PRIMARY_AUDIO_SOURCE);
        capabilities.append("- VOICE_RECOGNITION: ").append(voiceRecSupported ? "支持" : "不支持").append("\n");

        // Test MIC source
        boolean micSupported = testAudioSource(FALLBACK_AUDIO_SOURCE);
        capabilities.append("- MIC: ").append(micSupported ? "支持" : "不支持").append("\n");

        // Test other common sources
        int[] otherSources = {
            MediaRecorder.AudioSource.CAMCORDER,
            MediaRecorder.AudioSource.VOICE_CALL,
            MediaRecorder.AudioSource.VOICE_COMMUNICATION
        };

        String[] sourceNames = {"CAMCORDER", "VOICE_CALL", "VOICE_COMMUNICATION"};

        for (int i = 0; i < otherSources.length; i++) {
            boolean supported = testAudioSource(otherSources[i]);
            capabilities.append("- ").append(sourceNames[i]).append(": ")
                       .append(supported ? "支持" : "不支持").append("\n");
        }

        return capabilities.toString();
    }

    /**
     * Get audio file extension
     */
    public static String getAudioFileExtension() {
        return ".3gp";
    }

    /**
     * Check if file is an audio recording file
     */
    public static boolean isAudioRecordingFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        String name = file.getName().toLowerCase();
        return (name.endsWith(".3gp") || name.endsWith(".wav")) && (name.startsWith("voice_") || name.contains("voice"));
    }

    /**
     * Perform pre-flight checks before starting recording
     */
    private boolean performPreflightChecks() {
        // Check if audio recording is supported
        if (!isAudioRecordingSupported()) {
            notifyError("设备不支持音频录制功能");
            return false;
        }

        // Check storage folder
        String gpsLoggerFolder = preferenceHelper.getGpsLoggerFolder();
        if (gpsLoggerFolder == null || gpsLoggerFolder.trim().isEmpty()) {
            notifyError("存储文件夹未配置");
            return false;
        }

        File storageDir = new File(gpsLoggerFolder);
        if (!storageDir.exists()) {
            boolean created = storageDir.mkdirs();
            if (!created) {
                notifyError("无法创建存储文件夹");
                return false;
            }
        }

        // Check write permissions
        if (!storageDir.canWrite()) {
            notifyError("没有存储文件夹写入权限");
            return false;
        }

        // Check available storage space (require at least 10MB)
        long freeSpace = storageDir.getFreeSpace();
        long requiredSpace = 10 * 1024 * 1024; // 10MB
        if (freeSpace < requiredSpace) {
            notifyError("存储空间不足，需要至少10MB空间");
            return false;
        }

        LOG.debug("Pre-flight checks passed - storage: {}, free space: {}MB",
                 gpsLoggerFolder, freeSpace / (1024 * 1024));
        return true;
    }

    /**
     * Check if RECORD_AUDIO permission is granted
     */
    public boolean hasRecordAudioPermission() {
        return androidx.core.content.ContextCompat.checkSelfPermission(context,
                android.Manifest.permission.RECORD_AUDIO) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Get estimated file size for a recording duration
     * @param durationSeconds Recording duration in seconds
     * @return Estimated file size in bytes
     */
    public static long getEstimatedFileSize(int durationSeconds) {
        // Rough estimation: 44.1kHz * 16bit * mono = ~88KB per second
        // Add some overhead for WAV header and compression
        return durationSeconds * 100 * 1024; // 100KB per second
    }

    /**
     * Set up recording timeout to prevent runaway recordings
     */
    private void setupRecordingTimeout() {
        clearRecordingTimeout(); // Clear any existing timeout

        timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                LOG.warn("Recording timeout reached, automatically stopping recording");
                if (isRecording) {
                    cancelRecording();
                    notifyError("录音超时，已自动停止");
                }
            }
        };

        mainHandler.postDelayed(timeoutRunnable, RECORDING_TIMEOUT_MS);
        LOG.debug("Recording timeout set for {} minutes", RECORDING_TIMEOUT_MS / (60 * 1000));
    }

    /**
     * Clear recording timeout
     */
    private void clearRecordingTimeout() {
        if (timeoutRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
            LOG.debug("Recording timeout cleared");
        }
    }

    /**
     * Configure MediaRecorder with fallback strategy for audio source
     */
    private MediaRecorder configureMediaRecorder(String outputPath) throws Exception {
        MediaRecorder recorder = new MediaRecorder();

        // Try primary audio source first (VOICE_RECOGNITION)
        try {
            LOG.debug("Attempting to configure MediaRecorder with VOICE_RECOGNITION audio source");
            recorder.setAudioSource(PRIMARY_AUDIO_SOURCE);
            recorder.setOutputFormat(OUTPUT_FORMAT);
            recorder.setAudioEncoder(AUDIO_ENCODER);
            recorder.setOutputFile(outputPath);
            recorder.setAudioSamplingRate(SAMPLE_RATE);
            recorder.setAudioEncodingBitRate(BIT_RATE);

            // Test prepare to see if configuration works
            recorder.prepare();
            LOG.info("Successfully configured MediaRecorder with VOICE_RECOGNITION source");
            return recorder;

        } catch (Exception e) {
            LOG.warn("Failed to configure with VOICE_RECOGNITION source, trying fallback: {}", e.getMessage());

            // Release and recreate recorder for fallback
            try {
                recorder.release();
            } catch (Exception releaseError) {
                LOG.debug("Error releasing recorder during fallback", releaseError);
            }

            recorder = new MediaRecorder();

            // Try fallback audio source (MIC)
            try {
                LOG.debug("Attempting to configure MediaRecorder with MIC audio source");
                recorder.setAudioSource(FALLBACK_AUDIO_SOURCE);
                recorder.setOutputFormat(OUTPUT_FORMAT);
                recorder.setAudioEncoder(AUDIO_ENCODER);
                recorder.setOutputFile(outputPath);
                recorder.setAudioSamplingRate(SAMPLE_RATE);
                recorder.setAudioEncodingBitRate(BIT_RATE);

                recorder.prepare();
                LOG.info("Successfully configured MediaRecorder with MIC source (fallback)");
                return recorder;

            } catch (Exception fallbackError) {
                LOG.error("Failed to configure MediaRecorder with both audio sources", fallbackError);
                recorder.release();
                throw new Exception("Unable to configure MediaRecorder with any audio source: " +
                                  e.getMessage() + " / " + fallbackError.getMessage());
            }
        }
    }

    /**
     * Verify that recording is actually working
     */
    private void verifyRecordingStatus() {
        // Schedule a check after a short delay to verify file is being written
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isRecording && currentRecordingPath != null) {
                    File recordingFile = new File(currentRecordingPath);
                    if (recordingFile.exists()) {
                        long fileSize = recordingFile.length();
                        LOG.debug("Recording verification: file exists, size = {} bytes", fileSize);

                        if (fileSize > 0) {
                            LOG.info("Recording verification: SUCCESS - audio data is being written");
                            notifyStatusUpdate("录制正常 - 已写入 " + fileSize + " 字节");
                        } else {
                            LOG.warn("Recording verification: WARNING - file exists but is empty");
                            notifyStatusUpdate("录制警告 - 文件为空");
                        }
                    } else {
                        LOG.error("Recording verification: FAILED - recording file does not exist");
                        notifyStatusUpdate("录制失败 - 文件不存在");
                    }
                }
            }
        }, 1000); // Check after 1 second
    }
}
