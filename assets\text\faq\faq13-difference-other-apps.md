## How is this different from other logging apps?

It's meant to be more battery efficient. A lot of other apps, such as [OpenTracks](https://github.com/OpenTracksApp/OpenTracks), usually go with the assumption that you have a data connection available and your routes won't be very long. 
They use CPU wakelocks and log points extremely frequently with high accuracy. The aim of GPSLogger is to log points and stay quiet.  

To put it another way, OpenTracks or similar are better suited for runs; GPSLogger is suited for days out, hiking, photography.

[BasicAirData GPSLogger](https://play.google.com/store/apps/details?id=eu.basicairdata.graziano.gpslogger&hl=en&gl=US), [Ultra GPS Logger](https://play.google.com/store/apps/details?id=com.flashlight.lite.gps.logger&hl=en&gl=US) have similar names but aren't associated with this project. 