---
permalink: index.html
layout: base.html
---

    <header class="header">
        <div class="container"><img src="images/gps_icon05.png" class="pull-left logo">
            <h1><a href="https://gpslogger.app/">GPSLogger for Android</a></h1>
            <p class="description">A battery efficient GPS logging application</p>
            <section class="blurb">GPSLogger uses the GPS capabilities of your Android phone to log coordinates to GPS
                format files at regular intervals.
                This can be particularly useful if you want to geotag your photos after a day out or share your travel
                route with someone.
                The purpose of this application is to be battery efficient to save you battery power when abroad and
                last as long as
                possible.</section>
            <a href="#quicktour" class="button">Tour</a>
            <a href="#frequentlyaskedquestionsandissues" class="button">FAQ</a>
            <a href="#morescreenshots" class="button">Screenshots</a>

            <a href="https://github.com/mendhak/gpslogger/releases" class="pull-right">
                <img src="images/GitHub_Logo.png" height="40px" class="pull-right" style="margin-top:10px;">
                <img src="images/GitHub-Mark-64px.png" height="40px" class="pull-right" style="margin-top:10px;">
            </a>
            <a href="https://f-droid.org/packages/com.mendhak.gpslogger" class="pull-right">
                <img src="https://fdroid.gitlab.io/artwork/badge/get-it-on.png" alt="Get it on F-Droid" height="65px"
                    class="pull-right" />
            </a>


        </div>
    </header>
    <div class="container">
        <main>

            {%- for item in collections.tour -%}
            <section>
                <div class="lead">
                    {{ item.templateContent }}

                </div>
            </section>
            
            {%- endfor -%}

            <hr />

            {%- for item in collections.faq -%}
            <section>
                <div class="lead">
                    {{ item.templateContent }}

                </div>
            </section>

            {%- endfor -%}

            <hr />

            {%- for item in collections.more -%}
            <section>
                <div class="lead">
                    {{ item.templateContent }}

                </div>
            </section>

            {%- endfor -%}
        </main>
    </div>


    
    {% include "footer.rich.html" %}