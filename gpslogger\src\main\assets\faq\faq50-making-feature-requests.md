
## Logging to SMS or email

Logging to SMS (and email) is a frequently requested feature, however this has not been implemented for a few reasons. While logging can be set to be frequent (~1 second), sending an email or SMS every second would get this app banned very quickly and is also likely unsafe for the user. 
 
Although this *could* be mitigated by implementing specific logic to not allow SMS and emails to be sent so frequently, this would dramatically increase the complexity of the code and the flow of logic.  

## Can you implement a specific feature?
## Making feature requests

Please note that I work on GPSLogger in my spare time and I may not always have the time or resources to implement a feature. However, GPSLogger is open source. You are encouraged to contribute or get someone else to contribute a feature.

It's also worth mentioning that not every feature request can be catered to; every request is evaluated through the lens of whether it would benefit the majority of users, or whether it is an edge case specific to the submitter.  

You can submit your [feature requests on Github](https://github.com/mendhak/gpslogger/issues).


