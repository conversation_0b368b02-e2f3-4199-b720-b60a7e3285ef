## Load profiles from a file on your device

#### How can I define a preset file with my own values?

You can also share profiles using a file on the device.  

If you create a file in the GPSLogger default directory, a profile will appear in the menu named after the file.  For example, `xyz.properties` will appear as `xyz`. 

#### Special profile - `gpslogger.properties`

If you create a file in the GPSLogger default folder or specifically at `/sdcard/gpslogger.properties`, then GPSLogger will read this file each time it loads and apply those settings to the application, overriding whatever settings you have currently. 

 
