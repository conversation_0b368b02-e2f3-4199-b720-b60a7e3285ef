/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}
body {
    border-top: 6px solid #2391c9;
    background: #fff url(images/content-bg.png);
    margin: 0;
    padding: 0;
    width: 100%;
    outline: 0;
}
article,
aside,
details,
figcaption,
figure,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block
}
audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline
}
audio:not([controls]) {
    display: none;
    height: 0
}
[hidden],
template {
    display: none
}
a {
    background-color: transparent
}
a:active,
a:hover {
    outline: 0
}
abbr[title] {
    border-bottom: 1px dotted
}
b,
strong {
    font-weight: bold
}
dfn {
    font-style: italic
}
h1 {
    font-size: 2em;
    margin: 0.67em 0
}
mark {
    background: #ff0;
    color: #000
}
small {
    font-size: 80%
}
sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}
sup {
    top: -0.5em
}
sub {
    bottom: -0.25em
}
img {
    border: 0
}
svg:not(:root) {
    overflow: hidden
}
figure {
    margin: 1em 40px
}
hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0
}
pre {
    overflow: auto
}
code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}
button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0
}
button {
    overflow: visible
}
button,
select {
    text-transform: none
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer
}
button[disabled],
html input[disabled] {
    cursor: default
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0
}
input {
    line-height: normal
}
input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto
}
input[type="search"] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none
}
fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em
}
legend {
    border: 0;
    padding: 0
}
textarea {
    overflow: auto
}
optgroup {
    font-weight: bold
}
table {
    border-collapse: collapse;
    border-spacing: 0
}
td,
th {
    padding: 0
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
*:before,
*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
html {
    font-size: 10px;
    -webkit-tap-highlight-color: transparent
}
body {
    font-family: Lora, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.42857;
    color: #333;
    background-color: #fff
}
input,
button,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}
a {
    color: #337ab7;
    text-decoration: none
}
a:hover,
a:focus {
    color: #23527c;
    text-decoration: none
}
a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}
figure {
    margin: 0
}
img {
    vertical-align: middle
}
.img-responsive,
main img {
    display: block;
    max-width: 50%;
    height: auto
}
.img-rounded {
    border-radius: 6px
}
.img-thumbnail {
    padding: 4px;
    line-height: 1.42857;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    display: inline-block;
    max-width: 100%;
    height: auto
}
.img-circle {
    border-radius: 50%
}
hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}
[role="button"] {
    cursor: pointer
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit
}
h1 small,
h1 .small,
h2 small,
h2 .small,
h3 small,
h3 .small,
h4 small,
h4 .small,
h5 small,
h5 .small,
h6 small,
h6 .small,
.h1 small,
.h1 .small,
.h2 small,
.h2 .small,
.h3 small,
.h3 .small,
.h4 small,
.h4 .small,
.h5 small,
.h5 .small,
.h6 small,
.h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
    margin-top: 20px;
    margin-bottom: 10px
}
h1 small,
h1 .small,
.h1 small,
.h1 .small,
h2 small,
h2 .small,
.h2 small,
.h2 .small,
h3 small,
h3 .small,
.h3 small,
.h3 .small {
    font-size: 65%
}
h4,
.h4,
h5,
.h5,
h6,
.h6 {
    margin-top: 10px;
    margin-bottom: 10px
}
h4 small,
h4 .small,
.h4 small,
.h4 .small,
h5 small,
h5 .small,
.h5 small,
.h5 .small,
h6 small,
h6 .small,
.h6 small,
.h6 .small {
    font-size: 75%
}
h1,
.h1 {
    font-size: 42px
}
h2,
.h2 {
    font-size: 30px
}
h3,
.h3 {
    font-size: 24px
}
h4,
.h4 {
    font-size: 18px
}
h5,
.h5 {
    font-size: 14px
}
h6,
.h6 {
    font-size: 12px
}
p {
    margin: 0 0 10px
}
.lead {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.4
}
@media (min-width: 768px) {
    .lead {
        font-size: 21px
    }
}
small,
.small {
    font-size: 85%
}
mark,
.mark {
    background-color: #fcf8e3;
    padding: .2em
}
.text-left {
    text-align: left
}
.text-right {
    text-align: right
}
.text-center {
    text-align: center
}
.text-justify {
    text-align: justify
}
.text-nowrap {
    white-space: nowrap
}
.text-lowercase {
    text-transform: lowercase
}
.text-uppercase,
.initialism {
    text-transform: uppercase
}
.text-capitalize {
    text-transform: capitalize
}
.text-muted,
main h2 span {
    color: #777
}
.text-primary {
    color: #337ab7
}
a.text-primary:hover {
    color: #286090
}
.text-success {
    color: #3c763d
}
a.text-success:hover {
    color: #2b542c
}
.text-info {
    color: #31708f
}
a.text-info:hover {
    color: #245269
}
.text-warning {
    color: #8a6d3b
}
a.text-warning:hover {
    color: #66512c
}
.text-danger {
    color: #a94442
}
a.text-danger:hover {
    color: #843534
}
.bg-primary {
    color: #fff
}
.bg-primary {
    background-color: #337ab7
}
a.bg-primary:hover {
    background-color: #286090
}
.bg-success {
    background-color: #dff0d8
}
a.bg-success:hover {
    background-color: #c1e2b3
}
.bg-info {
    background-color: #d9edf7
}
a.bg-info:hover {
    background-color: #afd9ee
}
.bg-warning {
    background-color: #fcf8e3
}
a.bg-warning:hover {
    background-color: #f7ecb5
}
.bg-danger {
    background-color: #f2dede
}
a.bg-danger:hover {
    background-color: #e4b9b9
}
.page-header {
    padding-bottom: 9px;
    margin: 40px 0 20px;
    border-bottom: 1px solid #eee
}
ul,
ol {
    margin-top: 0;
    margin-bottom: 10px
}
ul ul,
ul ol,
ol ul,
ol ol {
    margin-bottom: 0
}
.list-unstyled {
    padding-left: 0;
    list-style: none
}
.list-inline {
    padding-left: 0;
    list-style: none;
    margin-left: -5px
}
.list-inline>li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px
}
dl {
    margin-top: 0;
    margin-bottom: 20px
}
dt,
dd {
    line-height: 1.42857
}
dt {
    font-weight: bold
}
dd {
    margin-left: 0
}
.dl-horizontal dd:before,
.dl-horizontal dd:after {
    content: " ";
    display: table
}
.dl-horizontal dd:after {
    clear: both
}
@media (min-width: 768px) {
    .dl-horizontal dt {
        float: left;
        width: 160px;
        clear: left;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap
    }
    .dl-horizontal dd {
        margin-left: 180px
    }
}
abbr[title],
abbr[data-original-title] {
    cursor: help;
    border-bottom: 1px dotted #777
}
.initialism {
    font-size: 90%
}
blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee
}
blockquote p {
    line-height: 2.5;
}
blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
    margin-bottom: 0
}
blockquote small,
blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.42857;
    color: #777
}
blockquote small:before,
blockquote .small:before {
    content: '\2014 \00A0'
}
.blockquote-reverse,
blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #eee;
    border-left: 0;
    text-align: right
}
.blockquote-reverse small:before,
.blockquote-reverse .small:before,
blockquote.pull-right small:before,
blockquote.pull-right .small:before {
    content: ''
}
.blockquote-reverse small:after,
.blockquote-reverse .small:after,
blockquote.pull-right small:after,
blockquote.pull-right .small:after {
    content: '\00A0 \2014'
}
address {
    margin-bottom: 20px;
    font-style: normal;
    line-height: 1.42857
}
code,
kbd,
pre,
samp {
    font-family: monospace, Menlo, Monaco, Consolas, "Courier New"
}
code {
    padding: 2px 4px;
    font-size: 1em;
    color: darkgreen;
    background-color: #f9f2f4;
    border-radius: 4px
}
kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: #fff;
    background-color: #333;
    border-radius: 3px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25)
}
kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: bold;
    box-shadow: none
}
pre {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 1.42857;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px
}
pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0
}
.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}
.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}
.container:before,
.container:after {
    content: " ";
    display: table
}
.container:after {
    clear: both
}
@media (min-width: 768px) {
    .container {
        width: 750px
    }
}
@media (min-width: 992px) {
    .container {
        width: 970px
    }
}
@media (min-width: 1200px) {
    .container {
        width: 1170px
    }
}
.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}
.container-fluid:before,
.container-fluid:after {
    content: " ";
    display: table
}
.container-fluid:after {
    clear: both
}
.row {
    margin-left: -15px;
    margin-right: -15px
}
.row:before,
.row:after {
    content: " ";
    display: table
}
.row:after {
    clear: both
}
fieldset {
    padding: 0;
    margin: 0;
    border: 0;
    min-width: 0
}
legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 20px;
    font-size: 21px;
    line-height: inherit;
    color: #333;
    border: 0;
    border-bottom: 1px solid #e5e5e5
}
label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold
}



.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.lead p img,
.pull-right {
    float: right !important
}

.pull-left {
    float: left !important
}

.hide {
    display: none !important
}
.show {
    display: block !important
}
.invisible {
    visibility: hidden
}
.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}
.hidden {
    display: none !important
}
.affix {
    position: fixed
}
@-ms-viewport {
    width: device-width
}

@media (max-width: 768px) {
    .container {
        margin: 0 15px
    }
}
header {
    display: block;
    width: 100%;
    

    -webkit-background-size: cover;
    -moz-background-size: cover;
    background-size: cover;
    -o-background-size: cover
}
header .headline {
    padding: 120px 0
}
header h1 {
    font-size: 60px;
    font-size: 60px;
    line-height: 65px;
    font-weight: bold;
    color: #1e83b6;
    letter-spacing: 3px;
    margin-bottom: 0px;
    margin-top: 42px;
    /*margin-left: 107px;*/
    
}
header h2 {
    font-size: 77px;
    background: #fff;
    background: rgba(255, 255, 255, 0.9)
}
@media (max-width: 1200px) {
    header h1 {
        font-size: 60px;
        line-height: 72px;
    }
    header h2 {
        font-size: 63px
    }
}
@media (max-width: 991px) {
    header h1 {
        font-size: 55px
    }
    header h2 {
        font-size: 50px
    }
}
@media (max-width: 668px) {
    header h1 {
        font-size: 70px
    }
    header h2 {
        font-size: 32px
    }
}
@media (max-width: 640px) {
    header .headline {
        padding: 75px 0 25px 0
    }
    header h1 {
        font-size: 60px
    }
    header h2 {
        font-size: 30px
    }
    img.logo {
        display:none;
    }
}
main hr {
    margin: 80px 0
}
main section {
    overflow: hidden
}
main img.pull-left {
    margin-right: 40px
}
main img.pull-right {
    margin-left: 40px
}
main h1 {
    font-size: 70px
}
main h2 {
    font-size: 50px
}
@media (max-width: 1200px) {
    main hr {
        margin: 50px 0
    }
    main h1 {
        font-size: 40px
    }
    main h2 {
        font-size: 35px
    }
    main img.pull-left {
        margin-right: 20px
    }
    main img.pull-right {
        margin-left: 20px
    }
}
@media (max-width: 991px) {
    main hr {
        margin: 40px 0
    }
    main h2 {
        font-size: 30px
    }
    main img {
        max-width: 50%
    }
    main img.pull-left {
        margin-right: 10px
    }
    main img.pull-right {
        margin-left: 10px
    }
}
@media (max-width: 768px) {
    main hr {
        margin: 40px 0
    }
    main h2 {
        font-size: 25px
    }
}
@media (max-width: 668px) {
    main hr {
        margin: 30px 0
    }
}
@media (max-width: 375px) {
    main hr {
        margin: 10px 0
    }
    main img {
        max-width: 100%
    }
    main img.pull-left {
        margin-right: 0;
        margin-bottom: 10px
    }
    main img.pull-right {
        margin-bottom: 10px;
        margin-left: 0
    }
}


/* page footer */

footer {
    border-top: 1px dashed #d2d2d2;
  margin: 3em 0;
}

footer .nav {
  text-align: center;
  margin-top: 5em;
  margin-bottom: 3.5em;
}

footer .nav a {
  padding: 0 0.5em;
  font-size: 1.2em;
  text-decoration: none;
}

footer .about {
  border-top: 1px dashed #d2d2d2;
  padding: 2.2em 3em;
  font-size: 0.7em;
  -webkit-column-count: 3;
  -moz-column-count: 3;
  -ms-column-count: 3;
  column-count: 3;
  -webkit-column-gap: 2em;
  -moz-column-gap: 2em;
  -ms-column-gap: 2em;
  column-gap: 2em;
}

footer .copy {
  text-align: center;
  font-size: 0.7em;
  font-style: italic;
  margin-top: 1em;
}

footer .copy, footer .copy a {
  color: #8e8e8e;
}




.content-wrap {
    font-size: 18px;
  width: 34em;
  margin: 0 auto;
}

/* Photostream */
footer ul.photostream {
    list-style: none;
    margin: 18px 0 24px 0;
    padding: 0;
    overflow: hidden;
    width: 100%;
    text-align: center;
}

footer ul.photostream li {
    
    display: inline-block;
    height: 150px;
    weight: 150px;
    margin: 0 12px 12px 0;
    padding: 0;
}

footer ul.photostream li a {
    border: none;
}



.button,
.button:visited,
button,
input[type="submit"],
input[type="reset"],
input[type="button"] {
   height: 42px;
   background: #3695c6;
   border: 1px solid #1C89C4;
	display: inline-block;

	font-family: "Open-sans", Sans-serif;
   font-size: 14px;
	font-weight: bold;
	text-decoration: none;
   letter-spacing: 0;
   color: #fff;
   line-height: 42px;

	cursor: pointer;
   padding-left: 15px;
   padding-right: 15px;
	margin-bottom: 18px;

    -webkit-transition: all .2s ease-in-out;
	-moz-transition: all .2s ease-in-out;
	-o-transition: all .2s ease-in-out;
	-ms-transition: all .2s ease-in-out;
	transition: all .2s ease-in-out;

   text-shadow: 0 -1px 0 rgba(0,0,0,0.3);

   box-shadow: 0px 1px 2px rgba(0,0,0,0.3), 0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

.button:hover,
button:hover,
input[type="submit"]:hover,
input[type="reset"]:hover,
input[type="button"]:hover {
   background: #7AA300;
   color: #fff;
   border: 1px solid #739900;
}

.button:active,
button:active,
input[type="submit"]:active,
input[type="reset"]:active,
input[type="button"]:active {
   background: #7AA300;
   color: #fff;
   border: 1px solid #739900;
}

.button.full-width,
button.full-width,
input[type="submit"].full-width,
input[type="reset"].full-width,
input[type="button"].full-width {
	width: 100%;
	padding-left: 0 !important;
	padding-right: 0 !important;
	text-align: center;
}

/* Fix for odd Mozilla border & padding issues */
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

p.description {
    font-family: "Noticia Text", Serif;
    font-size: 17px !important;
    line-height: 24px !important;
    min-height: 30px;
    font-weight: normal;
    font-style: italic;
    color: #a2a4a5;
    /*margin-left: 111px;*/
    margin-top: 6px;
    vertical-align: center;
}

section.blurb{
 line-height: 42px;  
 font-size: 19px; 
 color: #737373;
 padding-bottom: 10px;
}

img.logo {
    margin-top:42px; 
    height:85px;
    padding-right: 12px;
}
