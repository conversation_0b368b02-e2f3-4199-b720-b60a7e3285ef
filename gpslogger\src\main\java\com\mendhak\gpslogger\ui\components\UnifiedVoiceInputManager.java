/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 统一语音输入管理器 - 使用单音源同时处理语音识别和录音
 * 解决传统方案中的音频源冲突问题
 */
public class UnifiedVoiceInputManager {
    private static final Logger LOG = Logs.of(UnifiedVoiceInputManager.class);
    
    private final Activity activity;
    private final androidx.fragment.app.Fragment fragment;
    private final PreferenceHelper preferenceHelper;
    private final Handler mainHandler;
    
    // 统一音频处理器
    private UnifiedAudioProcessor audioProcessor;
    
    // 语音识别相关
    private VoiceRecognitionAdapter recognitionAdapter;
    private AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    // 录音相关
    private String currentRecordingPath;
    private String pendingAnnotationText;
    
    // 监听器
    private VoiceInputListener listener;
    
    /**
     * 语音输入监听器接口
     */
    public interface VoiceInputListener {
        void onVoiceInputStarted();
        void onVoiceInputResult(String text);
        void onVoiceInputError(String error);
        void onVoiceInputCancelled();
        void onRecordingStarted(String filePath);
        void onRecordingStopped(String filePath);
    }
    
    /**
     * 构造函数 - Fragment版本
     */
    public UnifiedVoiceInputManager(androidx.fragment.app.Fragment fragment, VoiceInputListener listener) {
        this.fragment = fragment;
        this.activity = fragment.getActivity();
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        initializeComponents();
    }
    
    /**
     * 构造函数 - Activity版本
     */
    public UnifiedVoiceInputManager(Activity activity, VoiceInputListener listener) {
        this.activity = activity;
        this.fragment = null;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        Context context = activity != null ? activity : (fragment != null ? fragment.getContext() : null);
        if (context == null) {
            LOG.error("Cannot initialize - no context available");
            return;
        }
        
        // 初始化统一音频处理器
        audioProcessor = new UnifiedAudioProcessor(context);
        
        // 初始化语音识别适配器
        recognitionAdapter = new VoiceRecognitionAdapter(context);

        // 检查语音识别是否真正可用
        if (recognitionAdapter != null && !recognitionAdapter.isSpeechRecognitionReallyAvailable()) {
            LOG.warn("Speech recognition may not be fully available, but will continue initialization");
        }
        
        // 设置音频数据监听器
        audioProcessor.setAudioDataListener(new UnifiedAudioProcessor.AudioDataListener() {
            @Override
            public void onAudioData(byte[] audioData, int length) {
                // 将音频数据发送给语音识别
                if (recognitionAdapter != null) {
                    recognitionAdapter.feedAudioData(audioData, length);
                }
            }
            
            @Override
            public void onAudioStarted() {
                LOG.debug("Audio processing started");
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputStarted());
                }
            }
            
            @Override
            public void onAudioStopped() {
                LOG.debug("Audio processing stopped");
                isProcessing.set(false);
            }
            
            @Override
            public void onAudioError(String error) {
                LOG.error("Audio processing error: {}", error);
                isProcessing.set(false);
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputError(error));
                }
            }
        });
        
        // 设置录音监听器
        audioProcessor.setRecordingListener(new UnifiedAudioProcessor.RecordingListener() {
            @Override
            public void onRecordingStarted(String filePath) {
                LOG.debug("Recording started: {}", filePath);
                if (listener != null) {
                    listener.onRecordingStarted(filePath);
                }
            }
            
            @Override
            public void onRecordingStopped(String filePath) {
                LOG.info("Recording stopped: {}", filePath);
                if (listener != null) {
                    listener.onRecordingStopped(filePath);
                }
            }
            
            @Override
            public void onRecordingError(String error) {
                LOG.error("Recording error: {}", error);
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputError("录音错误: " + error));
                }
            }
        });
        
        // 设置语音识别监听器
        recognitionAdapter.setRecognitionListener(new VoiceRecognitionAdapter.VoiceRecognitionListener() {
            @Override
            public void onRecognitionResult(String text) {
                LOG.info("Voice recognition result: '{}'", text);
                pendingAnnotationText = text;
                
                // 停止音频处理并保存录音
                String finalPath = audioProcessor.stopProcessing(text);
                
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputResult(text));
                }
            }
            
            @Override
            public void onRecognitionError(String error) {
                LOG.error("Voice recognition error: {}", error);
                
                // 取消音频处理
                audioProcessor.cancelProcessing();
                
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputError("语音识别失败: " + error));
                }
            }
            
            @Override
            public void onRecognitionTimeout() {
                LOG.warn("Voice recognition timeout");
                
                // 停止音频处理，使用默认文件名
                String finalPath = audioProcessor.stopProcessing("voice_timeout_" + System.currentTimeMillis());
                
                if (listener != null) {
                    mainHandler.post(() -> listener.onVoiceInputError("语音识别超时"));
                }
            }
        });
        
        LOG.debug("UnifiedVoiceInputManager initialized successfully");
    }
    
    /**
     * 开始语音输入（同时启动语音识别和录音）
     */
    public boolean startVoiceInput() {
        if (isProcessing.get()) {
            LOG.warn("Voice input already in progress");
            return false;
        }
        
        if (!preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is disabled in settings");
            return false;
        }
        
        try {
            // 检查语音识别是否可用
            if (recognitionAdapter == null) {
                LOG.error("Recognition adapter not initialized");
                if (listener != null) {
                    listener.onVoiceInputError("语音识别组件初始化失败，请检查设备是否支持语音识别");
                }
                return false;
            }

            // 生成录音文件路径
            currentRecordingPath = generateRecordingPath();

            // 先启动统一音频处理（录音）
            if (!audioProcessor.startProcessing(currentRecordingPath)) {
                LOG.error("Failed to start audio processing");
                if (listener != null) {
                    listener.onVoiceInputError("启动音频录制失败，请检查录音权限");
                }
                return false;
            }

            // 然后启动语音识别
            if (!recognitionAdapter.startRecognition()) {
                LOG.error("Failed to start voice recognition, stopping audio processing");
                audioProcessor.cancelProcessing();

                if (listener != null) {
                    listener.onVoiceInputError("语音识别启动失败，请确认已安装讯飞语记等语音识别应用");
                }
                return false;
            }

            isProcessing.set(true);
            LOG.info("Unified voice input started successfully");
            return true;

        } catch (Exception e) {
            LOG.error("Error starting unified voice input", e);
            if (listener != null) {
                listener.onVoiceInputError("启动语音输入失败: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 停止语音输入
     */
    public void stopVoiceInput() {
        if (!isProcessing.get()) {
            return;
        }
        
        LOG.debug("Stopping unified voice input");
        
        // 停止语音识别
        if (recognitionAdapter != null) {
            recognitionAdapter.stopRecognition();
        }
        
        // 停止音频处理
        if (audioProcessor != null) {
            String finalPath = audioProcessor.stopProcessing(pendingAnnotationText);
            LOG.debug("Final recording path: {}", finalPath);
        }
        
        isProcessing.set(false);
        currentRecordingPath = null;
        pendingAnnotationText = null;
        
        LOG.info("Unified voice input stopped");
    }
    
    /**
     * 取消语音输入
     */
    public void cancelVoiceInput() {
        if (!isProcessing.get()) {
            return;
        }
        
        LOG.debug("Cancelling unified voice input");
        
        // 停止语音识别
        if (recognitionAdapter != null) {
            recognitionAdapter.stopRecognition();
        }
        
        // 取消音频处理
        if (audioProcessor != null) {
            audioProcessor.cancelProcessing();
        }
        
        isProcessing.set(false);
        currentRecordingPath = null;
        pendingAnnotationText = null;
        
        if (listener != null) {
            mainHandler.post(() -> listener.onVoiceInputCancelled());
        }
        
        LOG.info("Unified voice input cancelled");
    }
    
    /**
     * 检查是否正在处理语音输入
     */
    public boolean isProcessing() {
        return isProcessing.get();
    }
    
    /**
     * 生成录音文件路径
     */
    private String generateRecordingPath() {
        String gpsLoggerFolder = preferenceHelper.getGpsLoggerFolder();
        if (gpsLoggerFolder == null || gpsLoggerFolder.trim().isEmpty()) {
            throw new RuntimeException("GPSLogger folder not configured");
        }
        
        File storageDir = new File(gpsLoggerFolder);
        if (!storageDir.exists()) {
            boolean created = storageDir.mkdirs();
            if (!created) {
                throw new RuntimeException("Cannot create storage directory");
            }
        }
        
        String fileName = "voice_temp_" + System.currentTimeMillis() + ".wav";
        return new File(storageDir, fileName).getAbsolutePath();
    }
    
    /**
     * 处理Activity结果（用于直接调用讯飞语记）
     */
    public void handleActivityResult(int requestCode, int resultCode, android.content.Intent data) {
        if (recognitionAdapter != null) {
            recognitionAdapter.handleActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        if (isProcessing.get()) {
            cancelVoiceInput();
        }

        if (recognitionAdapter != null) {
            recognitionAdapter.cleanup();
            recognitionAdapter = null;
        }

        audioProcessor = null;
        LOG.debug("UnifiedVoiceInputManager cleaned up");
    }
}
