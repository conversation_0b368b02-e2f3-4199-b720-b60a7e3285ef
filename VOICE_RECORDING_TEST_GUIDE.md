# GPSLogger语音录制功能测试指南

## 📋 测试概述

本指南提供了完整的语音录制功能测试步骤，确保功能正常工作并满足用户需求。

## 🔧 测试环境准备

### 1. 权限确认
- 确保应用已获得`RECORD_AUDIO`权限
- 确保应用已获得存储权限
- 检查设备音频录制功能是否正常

### 2. 存储空间检查
- 确保GPSLogger文件夹有足够空间（至少10MB）
- 检查文件夹写入权限

### 3. 设置确认
- 启用语音输入功能：设置 → General Options → 启用语音输入
- 配置注释模板（可选）：设置 → 记录细节设置 → 编辑Annotation模板

## 🧪 功能测试用例

### 测试用例1：基础语音录制（注释按钮）

**步骤：**
1. 打开GPSLogger应用
2. 导航到Annotation View
3. 点击任意"注释 X"按钮
4. 授予录音权限（如果首次使用）
5. 说出测试内容："这是测试录音"
6. 等待语音识别完成

**预期结果：**
- 语音识别成功，显示识别的文本
- 在GPSLogger文件夹中生成音频文件
- 文件名包含识别的文本内容
- 文件格式为.wav
- 音频文件可以正常播放

**验证方法：**
```bash
# 使用adb查看生成的文件
adb shell ls -la /storage/emulated/0/Android/data/com.mendhak.gpslogger/files/
# 查找包含"这是测试录音"的.wav文件
```

### 测试用例2：一键语音输入

**步骤：**
1. 在主界面点击语音输入按钮（麦克风图标）
2. 说出测试内容："一键语音测试"
3. 等待处理完成

**预期结果：**
- 语音识别成功
- 生成对应的音频文件
- 文件名基于模板处理后的内容

### 测试用例3：模板处理测试

**前置条件：**
- 设置注释模板为：`{date} {time} - {voice_text}`

**步骤：**
1. 使用语音输入说："模板测试"
2. 检查生成的文件

**预期结果：**
- 音频文件名格式：`2024-01-01 15:30 - 模板测试.wav`
- 文件内容为原始语音录音

### 测试用例4：错误处理测试

#### 4.1 存储空间不足
**步骤：**
1. 填满设备存储空间（保留少于10MB）
2. 尝试语音输入

**预期结果：**
- 显示"存储空间不足"错误
- 不会开始录制

#### 4.2 权限被拒绝
**步骤：**
1. 在系统设置中撤销录音权限
2. 尝试语音输入

**预期结果：**
- 显示权限请求对话框
- 如果拒绝，显示相应错误信息

#### 4.3 语音识别失败
**步骤：**
1. 在嘈杂环境中进行语音输入
2. 或者不说话直接取消

**预期结果：**
- 录制被取消，临时文件被删除
- 不会生成最终音频文件

### 测试用例5：长时间录制超时

**步骤：**
1. 开始语音输入但不结束（保持录制状态超过5分钟）

**预期结果：**
- 录制自动停止
- 显示超时错误信息
- 临时文件被清理

## 📊 性能测试

### 1. 录制质量测试
- 录制2秒短语音，检查音质
- 录制不同音量的语音
- 在不同环境噪音下测试

### 2. 文件大小测试
- 2秒录音应约为200KB
- 10秒录音应约为1MB
- 验证文件大小估算准确性

### 3. 并发测试
- 快速连续进行多次语音输入
- 验证每次都能正确生成独立的音频文件

## 🔍 日志分析

使用adb logcat查看详细日志：

```bash
# 查看语音录制相关日志
adb logcat | grep -E "(AudioRecording|VoiceInput)"

# 查看模板处理日志
adb logcat | grep -E "(Template|Annotation)"

# 查看文件操作日志
adb logcat | grep -E "(File|Storage)"
```

## ✅ 验收标准

### 功能性要求
- [ ] 语音识别开始时同步启动录制
- [ ] 语音识别结束时停止录制并保存文件
- [ ] 音频文件名基于模板处理后的注释内容
- [ ] 音频文件保存到GPSLogger文件夹
- [ ] 支持注释按钮和一键语音输入两种触发方式

### 质量要求
- [ ] 录制的音频清晰可听
- [ ] 文件格式为WAV
- [ ] 文件大小合理（约100KB/秒）
- [ ] 文件名不包含非法字符

### 错误处理要求
- [ ] 存储空间不足时正确提示
- [ ] 权限不足时正确处理
- [ ] 语音识别失败时清理临时文件
- [ ] 录制超时时自动停止

### 性能要求
- [ ] 录制启动延迟小于100ms
- [ ] 不影响语音识别性能
- [ ] 内存使用合理，无内存泄漏

## 🐛 常见问题排查

### 1. 录制不启动
- 检查RECORD_AUDIO权限
- 检查设备音频录制支持
- 查看logcat错误信息

### 2. 文件未生成
- 检查存储权限
- 检查GPSLogger文件夹路径
- 验证模板处理是否成功

### 3. 音频质量差
- 检查设备麦克风
- 测试在安静环境中录制
- 验证录制参数设置

### 4. 文件名异常
- 检查模板配置
- 验证特殊字符处理
- 查看文件名清理逻辑

## 📝 测试报告模板

```
测试日期：____
测试设备：____
Android版本：____
GPSLogger版本：____

测试结果：
□ 基础语音录制：通过/失败
□ 一键语音输入：通过/失败
□ 模板处理：通过/失败
□ 错误处理：通过/失败
□ 性能测试：通过/失败

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
