/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 统一音频处理器 - 解决语音识别和录音的音频源冲突问题
 * 使用单一AudioRecord源，同时为语音识别和录音保存提供音频数据
 */
public class UnifiedAudioProcessor {
    private static final Logger LOG = Logs.of(UnifiedAudioProcessor.class);
    
    // 音频参数配置 - 针对语音识别优化
    private static final int SAMPLE_RATE = 16000; // 16kHz适合语音识别
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int AUDIO_SOURCE = MediaRecorder.AudioSource.VOICE_RECOGNITION;
    
    // 缓冲区大小
    private static final int BUFFER_SIZE_MULTIPLIER = 4; // 增大缓冲区以提高稳定性
    
    // 录音超时设置（5分钟）
    private static final long RECORDING_TIMEOUT_MS = 5 * 60 * 1000;
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private Handler mainHandler;
    
    // 音频录制相关
    private AudioRecord audioRecord;
    private Thread recordingThread;
    private AtomicBoolean isRecording = new AtomicBoolean(false);
    private AtomicBoolean shouldStop = new AtomicBoolean(false);
    private int bufferSize;
    
    // 文件录制相关
    private FileOutputStream fileOutputStream;
    private String currentRecordingPath;
    private long recordedBytes = 0;
    
    // 语音识别相关
    private AudioDataListener audioDataListener;
    private Runnable timeoutRunnable;
    
    // 监听器接口
    public interface AudioDataListener {
        /**
         * 接收音频数据用于语音识别
         * @param audioData PCM音频数据
         * @param length 数据长度
         */
        void onAudioData(byte[] audioData, int length);
        
        /**
         * 音频处理开始
         */
        void onAudioStarted();
        
        /**
         * 音频处理结束
         */
        void onAudioStopped();
        
        /**
         * 音频处理错误
         * @param error 错误信息
         */
        void onAudioError(String error);
    }
    
    public interface RecordingListener {
        void onRecordingStarted(String filePath);
        void onRecordingStopped(String filePath);
        void onRecordingError(String error);
    }
    
    private RecordingListener recordingListener;
    
    /**
     * 构造函数
     */
    public UnifiedAudioProcessor(Context context) {
        this.context = context;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // 计算缓冲区大小
        int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
        this.bufferSize = minBufferSize * BUFFER_SIZE_MULTIPLIER;
        
        LOG.debug("UnifiedAudioProcessor initialized - buffer size: {} bytes", bufferSize);
    }
    
    /**
     * 设置音频数据监听器（用于语音识别）
     */
    public void setAudioDataListener(AudioDataListener listener) {
        this.audioDataListener = listener;
    }
    
    /**
     * 设置录音监听器
     */
    public void setRecordingListener(RecordingListener listener) {
        this.recordingListener = listener;
    }
    
    /**
     * 开始音频处理（同时启动语音识别和录音）
     * @param recordingFilePath 录音文件路径（可选，如果为null则只进行语音识别）
     * @return 是否成功启动
     */
    public boolean startProcessing(String recordingFilePath) {
        if (isRecording.get()) {
            LOG.warn("Audio processing already in progress");
            return false;
        }
        
        try {
            // 检查权限
            if (!hasRecordAudioPermission()) {
                notifyError("没有录音权限");
                return false;
            }
            
            // 初始化AudioRecord
            if (!initializeAudioRecord()) {
                return false;
            }
            
            // 初始化文件录制（如果需要）
            if (recordingFilePath != null) {
                if (!initializeFileRecording(recordingFilePath)) {
                    cleanup();
                    return false;
                }
            }
            
            // 启动录制线程
            shouldStop.set(false);
            recordingThread = new Thread(this::audioProcessingLoop, "UnifiedAudioProcessor");
            recordingThread.start();
            
            // 启动AudioRecord
            audioRecord.startRecording();
            isRecording.set(true);
            recordedBytes = 0;
            
            // 设置超时保护
            setupTimeout();
            
            // 通知监听器
            if (audioDataListener != null) {
                audioDataListener.onAudioStarted();
            }
            if (recordingFilePath != null && recordingListener != null) {
                mainHandler.post(() -> recordingListener.onRecordingStarted(recordingFilePath));
            }
            
            LOG.info("Unified audio processing started successfully");
            return true;
            
        } catch (Exception e) {
            LOG.error("Failed to start audio processing", e);
            cleanup();
            notifyError("启动音频处理失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止音频处理
     * @param finalFileName 最终录音文件名（用于重命名）
     * @return 最终录音文件路径
     */
    public String stopProcessing(String finalFileName) {
        if (!isRecording.get()) {
            LOG.warn("Audio processing is not running");
            return null;
        }
        
        LOG.debug("Stopping unified audio processing");
        
        // 停止录制
        shouldStop.set(true);
        isRecording.set(false);
        
        // 清除超时
        clearTimeout();
        
        // 停止AudioRecord
        try {
            if (audioRecord != null && audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                audioRecord.stop();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping AudioRecord", e);
        }
        
        // 等待录制线程结束
        if (recordingThread != null) {
            try {
                recordingThread.join(2000); // 最多等待2秒
            } catch (InterruptedException e) {
                LOG.warn("Recording thread join interrupted", e);
            }
        }
        
        // 处理录音文件
        String finalPath = null;
        if (currentRecordingPath != null) {
            finalPath = finalizeRecording(finalFileName);
        }
        
        // 清理资源
        cleanup();
        
        // 通知监听器
        if (audioDataListener != null) {
            audioDataListener.onAudioStopped();
        }
        if (finalPath != null && recordingListener != null) {
            final String path = finalPath;
            mainHandler.post(() -> recordingListener.onRecordingStopped(path));
        }
        
        LOG.info("Unified audio processing stopped, recorded {} bytes", recordedBytes);
        return finalPath;
    }
    
    /**
     * 取消音频处理
     */
    public void cancelProcessing() {
        if (!isRecording.get()) {
            return;
        }
        
        LOG.debug("Cancelling unified audio processing");
        
        // 停止录制
        shouldStop.set(true);
        isRecording.set(false);
        
        // 清除超时
        clearTimeout();
        
        // 停止AudioRecord
        try {
            if (audioRecord != null && audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                audioRecord.stop();
            }
        } catch (Exception e) {
            LOG.warn("Error stopping AudioRecord during cancel", e);
        }
        
        // 等待录制线程结束
        if (recordingThread != null) {
            try {
                recordingThread.join(1000);
            } catch (InterruptedException e) {
                LOG.warn("Recording thread join interrupted during cancel", e);
            }
        }
        
        // 删除临时录音文件
        if (currentRecordingPath != null) {
            File tempFile = new File(currentRecordingPath);
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                LOG.debug("Deleted temporary recording file: {}", deleted);
            }
        }
        
        // 清理资源
        cleanup();
        
        // 通知监听器
        if (audioDataListener != null) {
            audioDataListener.onAudioStopped();
        }
        
        LOG.info("Unified audio processing cancelled");
    }
    
    /**
     * 检查是否正在处理音频
     */
    public boolean isProcessing() {
        return isRecording.get();
    }
    
    /**
     * 获取已录制的字节数
     */
    public long getRecordedBytes() {
        return recordedBytes;
    }

    /**
     * 初始化AudioRecord
     */
    private boolean initializeAudioRecord() {
        try {
            audioRecord = new AudioRecord(
                AUDIO_SOURCE,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            );

            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                LOG.error("AudioRecord initialization failed");
                return false;
            }

            LOG.debug("AudioRecord initialized successfully");
            return true;

        } catch (Exception e) {
            LOG.error("Failed to initialize AudioRecord", e);
            return false;
        }
    }

    /**
     * 初始化文件录制
     */
    private boolean initializeFileRecording(String filePath) {
        try {
            currentRecordingPath = filePath;
            fileOutputStream = new FileOutputStream(filePath);

            // 写入WAV文件头（占位符，稍后更新）
            writeWavHeader(fileOutputStream, 0);

            LOG.debug("File recording initialized: {}", filePath);
            return true;

        } catch (IOException e) {
            LOG.error("Failed to initialize file recording", e);
            return false;
        }
    }

    /**
     * 音频处理主循环
     */
    private void audioProcessingLoop() {
        LOG.debug("Audio processing loop started");

        byte[] buffer = new byte[bufferSize];

        while (!shouldStop.get() && isRecording.get()) {
            try {
                int bytesRead = audioRecord.read(buffer, 0, buffer.length);

                if (bytesRead > 0) {
                    // 发送给语音识别监听器
                    if (audioDataListener != null) {
                        audioDataListener.onAudioData(buffer, bytesRead);
                    }

                    // 写入录音文件
                    if (fileOutputStream != null) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                        recordedBytes += bytesRead;
                    }

                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    LOG.error("AudioRecord read error: INVALID_OPERATION");
                    break;
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    LOG.error("AudioRecord read error: BAD_VALUE");
                    break;
                }

            } catch (Exception e) {
                LOG.error("Error in audio processing loop", e);
                break;
            }
        }

        LOG.debug("Audio processing loop ended");
    }

    /**
     * 完成录音并转换为WAV格式
     */
    private String finalizeRecording(String finalFileName) {
        if (currentRecordingPath == null) {
            return null;
        }

        try {
            // 关闭文件流
            if (fileOutputStream != null) {
                fileOutputStream.close();
                fileOutputStream = null;
            }

            // 更新WAV文件头
            updateWavHeader(currentRecordingPath, recordedBytes);

            // 重命名文件（如果需要）
            if (finalFileName != null && !finalFileName.isEmpty()) {
                File currentFile = new File(currentRecordingPath);
                String directory = currentFile.getParent();
                String finalPath = directory + File.separator + sanitizeFileName(finalFileName) + ".wav";

                File finalFile = new File(finalPath);
                if (currentFile.renameTo(finalFile)) {
                    LOG.info("Recording file renamed to: {}", finalPath);
                    return finalPath;
                } else {
                    LOG.warn("Failed to rename recording file, keeping original: {}", currentRecordingPath);
                    return currentRecordingPath;
                }
            }

            return currentRecordingPath;

        } catch (Exception e) {
            LOG.error("Error finalizing recording", e);
            return currentRecordingPath;
        }
    }

    /**
     * 写入WAV文件头
     */
    private void writeWavHeader(FileOutputStream out, long audioDataSize) throws IOException {
        long totalDataLen = audioDataSize + 36;
        long longSampleRate = SAMPLE_RATE;
        int channels = 1; // MONO
        long byteRate = SAMPLE_RATE * channels * 2; // 16-bit

        byte[] header = new byte[44];

        // RIFF header
        header[0] = 'R';
        header[1] = 'I';
        header[2] = 'F';
        header[3] = 'F';
        header[4] = (byte) (totalDataLen & 0xff);
        header[5] = (byte) ((totalDataLen >> 8) & 0xff);
        header[6] = (byte) ((totalDataLen >> 16) & 0xff);
        header[7] = (byte) ((totalDataLen >> 24) & 0xff);
        header[8] = 'W';
        header[9] = 'A';
        header[10] = 'V';
        header[11] = 'E';

        // fmt chunk
        header[12] = 'f';
        header[13] = 'm';
        header[14] = 't';
        header[15] = ' ';
        header[16] = 16; // fmt chunk size
        header[17] = 0;
        header[18] = 0;
        header[19] = 0;
        header[20] = 1; // PCM format
        header[21] = 0;
        header[22] = (byte) channels;
        header[23] = 0;
        header[24] = (byte) (longSampleRate & 0xff);
        header[25] = (byte) ((longSampleRate >> 8) & 0xff);
        header[26] = (byte) ((longSampleRate >> 16) & 0xff);
        header[27] = (byte) ((longSampleRate >> 24) & 0xff);
        header[28] = (byte) (byteRate & 0xff);
        header[29] = (byte) ((byteRate >> 8) & 0xff);
        header[30] = (byte) ((byteRate >> 16) & 0xff);
        header[31] = (byte) ((byteRate >> 24) & 0xff);
        header[32] = (byte) (channels * 2); // block align
        header[33] = 0;
        header[34] = 16; // bits per sample
        header[35] = 0;

        // data chunk
        header[36] = 'd';
        header[37] = 'a';
        header[38] = 't';
        header[39] = 'a';
        header[40] = (byte) (audioDataSize & 0xff);
        header[41] = (byte) ((audioDataSize >> 8) & 0xff);
        header[42] = (byte) ((audioDataSize >> 16) & 0xff);
        header[43] = (byte) ((audioDataSize >> 24) & 0xff);

        out.write(header, 0, 44);
    }

    /**
     * 更新WAV文件头中的数据大小
     */
    private void updateWavHeader(String filePath, long audioDataSize) {
        try (java.io.RandomAccessFile file = new java.io.RandomAccessFile(filePath, "rw")) {
            // 更新总文件大小（位置4-7）
            long totalDataLen = audioDataSize + 36;
            file.seek(4);
            file.write((int) (totalDataLen & 0xff));
            file.write((int) ((totalDataLen >> 8) & 0xff));
            file.write((int) ((totalDataLen >> 16) & 0xff));
            file.write((int) ((totalDataLen >> 24) & 0xff));

            // 更新数据块大小（位置40-43）
            file.seek(40);
            file.write((int) (audioDataSize & 0xff));
            file.write((int) ((audioDataSize >> 8) & 0xff));
            file.write((int) ((audioDataSize >> 16) & 0xff));
            file.write((int) ((audioDataSize >> 24) & 0xff));

            LOG.debug("WAV header updated for file: {}, data size: {} bytes", filePath, audioDataSize);

        } catch (Exception e) {
            LOG.error("Failed to update WAV header", e);
        }
    }

    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "voice_recording_" + System.currentTimeMillis();
        }

        // 移除非法字符
        String sanitized = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");

        // 限制长度
        if (sanitized.length() > 200) {
            sanitized = sanitized.substring(0, 200);
        }

        return sanitized.trim();
    }

    /**
     * 设置超时保护
     */
    private void setupTimeout() {
        clearTimeout();

        timeoutRunnable = () -> {
            LOG.warn("Audio processing timeout reached, stopping automatically");
            if (isRecording.get()) {
                cancelProcessing();
                notifyError("音频处理超时，已自动停止");
            }
        };

        mainHandler.postDelayed(timeoutRunnable, RECORDING_TIMEOUT_MS);
        LOG.debug("Audio processing timeout set for {} minutes", RECORDING_TIMEOUT_MS / (60 * 1000));
    }

    /**
     * 清除超时保护
     */
    private void clearTimeout() {
        if (timeoutRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            // 停止AudioRecord
            if (audioRecord != null) {
                if (audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                    audioRecord.stop();
                }
                audioRecord.release();
                audioRecord = null;
            }

            // 关闭文件流
            if (fileOutputStream != null) {
                fileOutputStream.close();
                fileOutputStream = null;
            }

        } catch (Exception e) {
            LOG.warn("Error during cleanup", e);
        }

        isRecording.set(false);
        shouldStop.set(false);
        recordingThread = null;
        currentRecordingPath = null;
        recordedBytes = 0;

        clearTimeout();
    }

    /**
     * 通知错误
     */
    private void notifyError(String error) {
        if (audioDataListener != null) {
            audioDataListener.onAudioError(error);
        }
        if (recordingListener != null) {
            mainHandler.post(() -> recordingListener.onRecordingError(error));
        }
    }

    /**
     * 检查录音权限
     */
    private boolean hasRecordAudioPermission() {
        return androidx.core.content.ContextCompat.checkSelfPermission(context,
                android.Manifest.permission.RECORD_AUDIO) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 获取音频参数信息
     */
    public static String getAudioInfo() {
        return String.format("采样率: %d Hz, 声道: 单声道, 位深: 16位, 音频源: VOICE_RECOGNITION", SAMPLE_RATE);
    }

    /**
     * 检查设备是否支持音频录制
     */
    public static boolean isAudioRecordingSupported(Context context) {
        try {
            int minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
            if (minBufferSize == AudioRecord.ERROR_BAD_VALUE || minBufferSize == AudioRecord.ERROR) {
                return false;
            }

            AudioRecord testRecord = new AudioRecord(
                AUDIO_SOURCE,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                minBufferSize
            );

            boolean supported = testRecord.getState() == AudioRecord.STATE_INITIALIZED;
            testRecord.release();

            return supported;

        } catch (Exception e) {
            return false;
        }
    }
}
