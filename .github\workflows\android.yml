name: Android CI

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v1
      
    - name: Set up Java 17
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        
    - name: Build with Gradle
      run: ./gradlew assembleDebugUnitTest -Dpre-dex=false --no-configuration-cache
      
    - name: Run Unit Tests
      run: ./gradlew testDebugUnitTest -Dpre-dex=false -q --no-configuration-cache
